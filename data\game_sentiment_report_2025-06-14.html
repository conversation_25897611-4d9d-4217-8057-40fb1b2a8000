<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏舆情可视化日报</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* 主色调 */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-accent: #EFF6FF;
            --accent-primary: #3B82F6;
            --accent-secondary: #10B981;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-light: #E5E7EB;
            --border-medium: #D1D5DB;
            
            /* 用户层级色彩 */
            --super-r: linear-gradient(135deg, #A855F7, #C084FC);
            --big-r: linear-gradient(135deg, #3B82F6, #60A5FA);
            --mid-r: linear-gradient(135deg, #10B981, #34D399);
            --small-r: linear-gradient(135deg, #F59E0B, #FBBF24);
            --zero-pay: linear-gradient(135deg, #8B5CF6, #A78BFA);
            
            /* 健康度色彩 */
            --health-excellent: #059669;
            --health-good: #0284C7;
            --health-warning: #EA580C;
            --health-critical: #DC2626;
            
            /* 风险等级色彩 */
            --risk-high: #DC2626;
            --risk-medium: #EA580C;
            --risk-low: #059669;
            
            /* 字体大小 */
            --font-title-main: 2.5rem;
            --font-title-sub: 2rem;
            --font-title-card: 1.5rem;
            --font-body: 1rem;
            --font-caption: 0.875rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'Microsoft YaHei', 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas:
                "overview overview overview"
                "topics risks health"
                "compare wordcloud players";
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 1.5rem;
            padding: 2rem;
            min-height: 100vh;
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: var(--border-medium);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: var(--font-title-card);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card h3 i {
            color: var(--accent-primary);
        }

        /* 主卡片：舆情总览 */
        .main-overview-card {
            grid-area: overview;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .title-section h1 {
            font-size: var(--font-title-main);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-section h1 i {
            color: var(--accent-primary);
        }

        .date-badge {
            background: var(--accent-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .health-score-large {
            display: flex;
            align-items: center;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            text-align: center;
            position: relative;
        }

        .score-value {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .score-label {
            font-size: var(--font-caption);
            margin-top: 0.25rem;
        }

        .global-metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            background: var(--bg-accent);
            transform: translateY(-1px);
        }

        .metric-item i {
            font-size: 1.5rem;
            color: var(--accent-primary);
            width: 40px;
            text-align: center;
        }

        .metric-content .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
        }

        .metric-content .label {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .core-insight {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-primary);
        }

        .core-insight h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .core-insight p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .insight-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .insight-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .insight-tag.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .insight-tag.warning {
            background: #FEF3C7;
            color: #92400E;
        }

        .insight-tag.positive {
            background: #DCFCE7;
            color: #166534;
        }

        /* 热点话题卡片 */
        .hot-topics-card {
            grid-area: topics;
        }

        .topics-ranking {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .topic-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-secondary);
            transition: all 0.3s ease;
        }

        .topic-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .topic-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .topic-header .rank {
            background: var(--accent-secondary);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-caption);
            font-weight: 600;
        }

        .topic-header .title {
            flex: 1;
            margin-left: 1rem;
            font-weight: 600;
        }

        .impact-badge {
            background: var(--bg-accent);
            color: var(--accent-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
        }

        .topic-details {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .negative-rate, .affected-levels {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .representative-quote {
            font-style: italic;
            color: var(--text-secondary);
            font-size: var(--font-caption);
            border-left: 2px solid var(--border-light);
            padding-left: 0.75rem;
        }

        /* 风险预警卡片 */
        .risk-radar-card {
            grid-area: risks;
        }

        .risk-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .risk-category {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--risk-medium);
        }

        .risk-category.severity-高 {
            border-left-color: var(--risk-high);
        }

        .risk-category.severity-中 {
            border-left-color: var(--risk-medium);
        }

        .risk-category.severity-低 {
            border-left-color: var(--risk-low);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .category-name {
            font-weight: 600;
            flex: 1;
        }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
            color: white;
            background: var(--risk-medium);
        }

        .severity-badge.severity-高 {
            background: var(--risk-high);
        }

        .severity-badge.severity-中 {
            background: var(--risk-medium);
        }

        .severity-badge.severity-低 {
            background: var(--risk-low);
        }

        .affected-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .level-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .risk-description {
            color: var(--text-secondary);
            font-size: var(--font-caption);
            line-height: 1.6;
        }

        /* 用户群体健康度卡片 */
        .user-health-card {
            grid-area: health;
        }

        .health-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .level-health-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .level-info {
            flex: 1;
        }

        .level-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-count {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .health-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.2rem;
        }

        .level-status {
            text-align: center;
        }

        .status-text {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            display: block;
        }

        .trend-arrow {
            font-size: 1.5rem;
            margin-top: 0.25rem;
        }

        /* 版本对比卡片 */
        .version-compare-card {
            grid-area: compare;
        }

        .compare-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .version-column {
            flex: 1;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .version-column h4 {
            margin-bottom: 1rem;
            color: var(--accent-primary);
            font-size: 1.2rem;
        }

        .version-metrics {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .version-metrics .health-score {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .version-metrics .risk-count,
        .version-metrics .hot-issue {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .vs-divider {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .key-differences {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
        }

        .key-differences h5 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .key-differences ul {
            list-style: none;
        }

        .key-differences li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
            color: var(--text-secondary);
            font-size: var(--font-caption);
        }

        /* 词云卡片 */
        .wordcloud-card {
            grid-area: wordcloud;
        }

        .word-cloud-container {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            min-height: 200px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .word-bubble {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .word-bubble[data-sentiment="negative"] {
            background: #FEE2E2;
            color: #991B1B;
        }

        .word-bubble[data-sentiment="neutral"] {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .word-bubble[data-sentiment="positive"] {
            background: #DCFCE7;
            color: #166534;
        }

        .word-bubble:hover {
            transform: scale(1.1);
        }

        .sentiment-legend {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-caption);
        }

        .legend-item:before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .legend-item.positive:before {
            background: #166534;
        }

        .legend-item.neutral:before {
            background: var(--accent-primary);
        }

        .legend-item.negative:before {
            background: #991B1B;
        }

        /* 重点关注玩家卡片 */
        .key-players-card {
            grid-area: players;
        }

        .players-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .player-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .player-info {
            flex: 1;
        }

        .player-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .player-level {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .player-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .message-count {
            font-weight: 600;
            color: var(--text-primary);
        }

        .sentiment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .sentiment-badge.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .sentiment-badge.neutral {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .sentiment-badge.positive {
            background: #DCFCE7;
            color: #166534;
        }

        .attention-reason {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            line-height: 1.5;
            max-width: 200px;
        }

        /* 健康度分数样式 */
        .health-indicator[data-score^="8"],
        .health-indicator[data-score^="9"],
        .score-circle[data-score^="8"],
        .score-circle[data-score^="9"] {
            background: var(--health-excellent);
        }

        .health-indicator[data-score^="6"],
        .health-indicator[data-score^="7"],
        .score-circle[data-score^="6"],
        .score-circle[data-score^="7"] {
            background: var(--health-good);
        }

        .health-indicator[data-score^="4"],
        .health-indicator[data-score^="5"],
        .score-circle[data-score^="4"],
        .score-circle[data-score^="5"] {
            background: var(--health-warning);
        }

        .health-indicator[data-score^="0"],
        .health-indicator[data-score^="1"],
        .health-indicator[data-score^="2"],
        .health-indicator[data-score^="3"],
        .score-circle[data-score^="0"],
        .score-circle[data-score^="1"],
        .score-circle[data-score^="2"],
        .score-circle[data-score^="3"] {
            background: var(--health-critical);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-areas:
                    "overview overview"
                    "topics risks"
                    "health compare"
                    "wordcloud players";
                grid-template-columns: 1fr 1fr;
            }

            .global-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas:
                    "overview"
                    "topics"
                    "risks"
                    "health"
                    "compare"
                    "wordcloud"
                    "players";
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .global-metrics-grid {
                grid-template-columns: 1fr;
            }

            .overview-header {
                flex-direction: column;
                gap: 1rem;
            }

            .compare-container {
                flex-direction: column;
            }

            .vs-divider {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 主卡片：舆情总览 -->
        <div class="main-overview-card card">
            <div class="overview-header">
                <div class="title-section">
                    <h1><i class="fas fa-gamepad"></i> 游戏舆情日报</h1>
                    <div class="date-badge">2025年06月14日</div>
                </div>
                <div class="health-score-large">
                    <div class="score-circle" data-score="49">
                        <span class="score-value">49</span>
                        <span class="score-label">综合健康度</span>
                    </div>
                </div>
            </div>
            
            <div class="global-metrics-grid">
                <div class="metric-item">
                    <i class="fas fa-comments"></i>
                    <div class="metric-content">
                        <span class="value">388,966</span>
                        <span class="label">总消息数</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-users"></i>
                    <div class="metric-content">
                        <span class="value">32,817</span>
                        <span class="label">活跃用户</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="metric-content">
                        <span class="value">11</span>
                        <span class="label">高风险警报</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="metric-content">
                        <span class="value">45.9%</span>
                        <span class="label">负面情绪占比</span>
                    </div>
                </div>
            </div>
            
            <div class="core-insight">
                <h3><i class="fas fa-lightbulb"></i> 今日核心洞察</h3>
                <p>今日舆情健康度不佳，核心问题集中在游戏平衡性与付费体验上。全服玩家普遍反映匹配机制不公，导致挫败感强烈。高付费用户对核心养成系统（如驯化、洗练）的低回报率表达严重不满，付费意愿显著降低。同时，社区内冲突频发，言语攻击和内战问题突出，加剧了负面氛围。各层级均出现显著的玩家流失风险，需紧急关注并采取措施。</p>
                <div class="insight-tags">
                    <span class="insight-tag negative">玩家流失风险高企</span>
<span class="insight-tag negative">社区环境严重恶化</span>
<span class="insight-tag warning">付费体验普遍不佳</span>
                </div>
            </div>
        </div>

        <!-- 热点话题卡片 -->
        <div class="hot-topics-card card">
            <h3><i class="fas fa-fire"></i> 全服热点话题</h3>
            <div class="topics-ranking">
                <div class="topic-item">
    <div class="topic-header">
        <span class="rank">#1</span>
        <span class="title">游戏平衡与匹配机制</span>
        <span class="impact-badge">全服热议</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 77%</div>
        <div class="affected-levels">影响用户层级：大R, 中R, 小R</div>
    </div>
    <div class="representative-quote">"这什么烂匹配机制哦"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#2</span>
        <span class="title">核心养成体验 (驯化/基因/洗练)</span>
        <span class="impact-badge">全服热议</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 60%</div>
        <div class="affected-levels">影响用户层级：超R, 大R, 中R</div>
    </div>
    <div class="representative-quote">"我驯化了21次才成功，保底24次……"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#3</span>
        <span class="title">氪金体验与付费价值</span>
        <span class="impact-badge">全服热议</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 72%</div>
        <div class="affected-levels">影响用户层级：超R, 大R, 中R</div>
    </div>
    <div class="representative-quote">"不氪没东西 氪了也没东西 氪锤子"</div>
</div>
            </div>
        </div>

        <!-- 风险预警雷达 -->
        <div class="risk-radar-card card">
            <h3><i class="fas fa-exclamation-triangle"></i> 风险预警雷达</h3>
            <div class="risk-matrix">
                <div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">玩家流失风险</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
        <span class="level-tag">小R</span>
    </div>
    <div class="risk-description">综合各层级用户因付费体验差、养成回报低、社区环境恶劣、游戏不平衡等问题，已产生强烈倦怠感并明确表达退游或停氪意向，构成重大威胁。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">社区环境恶化</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
    </div>
    <div class="risk-description">玩家间频繁出现人身攻击、谩骂、内战等负面互动，严重影响游戏内社交体验，可能导致玩家关系紧张甚至退游。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">游戏平衡性与数值设计</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
        <span class="level-tag">小R</span>
    </div>
    <div class="risk-description">玩家普遍抱怨跨服匹配不公、特定兵种或巨兽数值超标，导致高投入玩家也感到无力，严重破坏PVP公平性。</div>
</div>
<div class="risk-category severity-中">
    <div class="category-header">
        <span class="category-name">养成系统体验差</span>
        <span class="severity-badge severity-中">中</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
    </div>
    <div class="risk-description">装备洗练、基因驯化等核心养成系统随机性过高，投入巨大但回报不确定，引发玩家强烈不满。</div>
</div>
            </div>
        </div>

        <!-- 用户群体健康度 -->
        <div class="user-health-card card">
            <h3><i class="fas fa-users"></i> 用户群体健康度</h3>
            <div class="health-matrix">
                <div class="level-health-item">
    <div class="level-info">
        <div class="level-name">超R用户</div>
        <div class="user-count">628人</div>
    </div>
    <div class="health-indicator" data-score="50">
        <span class="score">50</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">大R用户</div>
        <div class="user-count">2,163人</div>
    </div>
    <div class="health-indicator" data-score="55">
        <span class="score">55</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">中R用户</div>
        <div class="user-count">5,620人</div>
    </div>
    <div class="health-indicator" data-score="48">
        <span class="score">48</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">小R用户</div>
        <div class="user-count">9,160人</div>
    </div>
    <div class="health-indicator" data-score="48">
        <span class="score">48</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">零氪用户</div>
        <div class="user-count">数据缺失</div>
    </div>
    <div class="health-indicator" data-score="30">
        <span class="score">30</span>
    </div>
    <div class="level-status">
        <span class="status-text">较差</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
            </div>
        </div>

        <!-- 版本对比分析 -->
        <div class="version-compare-card card">
            <h3><i class="fas fa-balance-scale"></i> 版本对比分析</h3>
            <div class="compare-container">
                <div class="version-column">
                    <h4>E版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">47</div>
                        <div class="risk-count">7个高风险点</div>
                        <div class="hot-issue">游戏平衡与匹配问题</div>
                    </div>
                </div>
                <div class="vs-divider">VS</div>
                <div class="version-column">
                    <h4>D版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">50</div>
                        <div class="risk-count">4个高风险点</div>
                        <div class="hot-issue">驯化/基因系统问题</div>
                    </div>
                </div>
            </div>
            <div class="key-differences">
                <h5>关键差异</h5>
                <ul>
                    <li>E版用户健康度(47)显著低于D版(50)，高风险点(7个)也多于D版(4个)。</li>
<li>E版玩家核心痛点是“游戏平衡与匹配机制”，普遍感到PVP体验差。</li>
<li>D版玩家更集中抱怨“驯化/基因”等核心养成系统的低回报率和高失败率。</li>
<li>E版社区矛盾更激烈，玩家间冲突和对策划的直接指责更为普遍。</li>
                </ul>
            </div>
        </div>

        <!-- 舆情词云分析 -->
        <div class="wordcloud-card card">
            <h3><i class="fas fa-tags"></i> 舆情词云分析</h3>
            <div class="word-cloud-container">
                <span class="word-bubble" data-sentiment="neutral" data-frequency="6180" style="font-size: 2rem;">兵</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="4835" style="font-size: 2rem;">资源</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="4030" style="font-size: 2rem;">集结</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="2820" style="font-size: 2rem;">军团</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="2175" style="font-size: 2rem;">基因</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1810" style="font-size: 2rem;">驯化</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1760" style="font-size: 2rem;">超导</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="1650" style="font-size: 2rem;">兽王</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="1570" style="font-size: 2rem;">医院</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="960" style="font-size: 2rem;">打不过</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="910" style="font-size: 2rem;">氪金</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="900" style="font-size: 2rem;">失败</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="620" style="font-size: 2rem;">匹配</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="600" style="font-size: 2rem;">伤兵</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="430" style="font-size: 1.8rem;">洗练</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="300" style="font-size: 1.8rem;">退游</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="250" style="font-size: 1.6rem;">属性</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="100" style="font-size: 1.4rem;">骂</span>
            </div>
            <div class="sentiment-legend">
                <span class="legend-item positive">正面词汇</span>
                <span class="legend-item neutral">中性词汇</span>
                <span class="legend-item negative">负面词汇</span>
            </div>
        </div>

        <!-- 重点关注玩家 -->
        <div class="key-players-card card">
            <h3><i class="fas fa-crown"></i> 重点关注玩家</h3>
            <div class="players-list">
                <div class="player-item">
    <div class="player-avatar">不</div>
    <div class="player-info">
        <div class="player-name">不出红导弃游</div>
        <div class="player-level">超R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">78条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">多次提及“弃游”，对氪金活动和抽奖保底机制极度不满，是潜在流失风险用户。</div>
</div>
<div class="player-item">
    <div class="player-avatar">以</div>
    <div class="player-info">
        <div class="player-name">以爱为名</div>
        <div class="player-level">超R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">95条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">多次表达游戏体验差、想退游的强烈情绪，是潜在流失风险用户。</div>
</div>
<div class="player-item">
    <div class="player-avatar">五</div>
    <div class="player-info">
        <div class="player-name">五彩斑斓的黑</div>
        <div class="player-level">大R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">65条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">活跃且多次表达对游戏系统（驯化、基因、超导）的不满，以及对氪金体验的吐槽，是潜在的流失风险用户。</div>
</div>
<div class="player-item">
    <div class="player-avatar">奥</div>
    <div class="player-info">
        <div class="player-name">奥迪车</div>
        <div class="player-level">小R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">25条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">频繁抱怨游戏机制和玩家行为，表达对游戏环境的不满，提及“投敌”和“骂人”，可能煽动负面情绪。</div>
</div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为词云添加点击效果
            const wordBubbles = document.querySelectorAll('.word-bubble');
            wordBubbles.forEach(bubble => {
                bubble.addEventListener('click', function() {
                    const sentiment = this.getAttribute('data-sentiment');
                    const frequency = this.getAttribute('data-frequency') || Math.floor(Math.random() * 100) + 1;
                    alert(`词汇: ${this.textContent}\n情感倾向: ${sentiment}\n出现频次: ${frequency}`);
                });
            });

            // 为健康度指示器添加悬停效果
            const healthIndicators = document.querySelectorAll('.health-indicator, .score-circle');
            healthIndicators.forEach(indicator => {
                indicator.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });
                
                indicator.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 为卡片添加轻微的3D效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 20;
                    const rotateY = (centerX - x) / 20;
                    
                    this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-2px)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                });
            });
        });
    </script>
</body>
</html>