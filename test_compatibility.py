#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容性测试脚本
验证简化版AI API系统是否与现有代码兼容
"""

import sys
import os
import json
from datetime import datetime


def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from model_config import get_model_config, ModelConfig
        print("✅ model_config 导入成功")
        
        from ai_analysis import call_gemini_api as ai_call
        print("✅ ai_analysis.call_gemini_api 导入成功")
        
        from html_generator import call_gemini_api as html_call
        print("✅ html_generator.call_gemini_api 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        print("✅ 配置实例创建成功")
        
        # 测试配置内容
        if hasattr(config, 'config'):
            providers = config.config.get('providers', {})
            models = config.config.get('models', {})
            
            print(f"✅ 提供商数量: {len(providers)}")
            print(f"✅ 模型配置数量: {len(models)}")
            
            # 检查必要的配置
            if 'gemini' in providers:
                print("✅ Gemini 提供商配置存在")
            else:
                print("❌ Gemini 提供商配置缺失")
                return False
                
            if 'step1_analysis' in models and 'step2_html_generation' in models:
                print("✅ 必要的步骤配置存在")
            else:
                print("❌ 必要的步骤配置缺失")
                return False
                
            return True
        else:
            print("❌ 配置对象结构异常")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_api_call_structure():
    """测试API调用结构"""
    print("\n🔍 测试API调用结构...")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        
        # 测试方法存在性
        if hasattr(config, 'call_api'):
            print("✅ call_api 方法存在")
        else:
            print("❌ call_api 方法不存在")
            return False
            
        if hasattr(config, 'get_model_params'):
            print("✅ get_model_params 方法存在")
        else:
            print("❌ get_model_params 方法不存在")
            return False
            
        # 测试参数获取
        try:
            params1 = config.get_model_params('step1_analysis')
            params2 = config.get_model_params('step2_html_generation')
            print("✅ 步骤参数获取成功")
            
            # 检查参数结构
            required_keys = ['provider', 'model', 'temperature', 'max_tokens']
            for key in required_keys:
                if key in params1 and key in params2:
                    print(f"✅ 参数 {key} 存在")
                else:
                    print(f"❌ 参数 {key} 缺失")
                    return False
                    
        except Exception as e:
            print(f"❌ 参数获取失败: {e}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ API调用结构测试失败: {e}")
        return False


def test_ai_analysis_compatibility():
    """测试ai_analysis.py兼容性"""
    print("\n🔍 测试 ai_analysis.py 兼容性...")
    
    try:
        from ai_analysis import call_gemini_api, load_prompt_template
        
        # 测试函数存在性
        print("✅ call_gemini_api 函数可调用")
        
        # 测试prompt模板加载
        if os.path.exists('prompt1.md'):
            template = load_prompt_template()
            if template:
                print("✅ prompt1.md 模板加载成功")
            else:
                print("⚠️ prompt1.md 模板加载失败")
        else:
            print("⚠️ prompt1.md 文件不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ ai_analysis.py 兼容性测试失败: {e}")
        return False


def test_html_generator_compatibility():
    """测试html_generator.py兼容性"""
    print("\n🔍 测试 html_generator.py 兼容性...")
    
    try:
        from html_generator import call_gemini_api, load_prompt2_template
        
        # 测试函数存在性
        print("✅ call_gemini_api 函数可调用")
        
        # 测试prompt模板加载
        if os.path.exists('prompt2.md'):
            template = load_prompt2_template()
            if template:
                print("✅ prompt2.md 模板加载成功")
            else:
                print("⚠️ prompt2.md 模板加载失败")
        else:
            print("⚠️ prompt2.md 文件不存在")
            
        return True
        
    except Exception as e:
        print(f"❌ html_generator.py 兼容性测试失败: {e}")
        return False


def test_mock_api_call():
    """测试模拟API调用（不实际发送请求）"""
    print("\n🔍 测试模拟API调用...")
    
    try:
        from model_config import get_model_config
        
        config = get_model_config()
        
        # 准备测试消息
        test_messages = [
            {
                "role": "system",
                "content": "你是一个测试助手"
            },
            {
                "role": "user",
                "content": "这是一个测试消息，请简单回复"
            }
        ]
        
        # 测试参数构建（不实际调用API）
        try:
            params = config.get_model_params('step1_analysis')
            client = config.get_client(params['provider'])
            
            # 构建API参数
            api_params = {
                "model": params["model"],
                "messages": test_messages,
                "temperature": params.get("temperature", 0.3),
                "max_tokens": params.get("max_tokens", 8000),
                "top_p": params.get("top_p", 0.95)
            }
            
            if "reasoning_effort" in params:
                api_params["reasoning_effort"] = params["reasoning_effort"]
                
            print("✅ API参数构建成功")
            print(f"✅ 使用模型: {api_params['model']}")
            print(f"✅ 使用提供商: {params['provider']}")
            
            return True
            
        except Exception as e:
            print(f"❌ API参数构建失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 模拟API调用测试失败: {e}")
        return False


def test_file_structure():
    """测试文件结构"""
    print("\n🔍 测试文件结构...")
    
    required_files = [
        'model_config.py',
        'model_config.json',
        'ai_analysis.py',
        'html_generator.py',
        'main.py'
    ]
    
    optional_files = [
        'prompt1.md',
        'prompt2.md',
        'TA.py'
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 缺失")
            all_good = False
            
    for file in optional_files:
        if os.path.exists(file):
            print(f"✅ {file} 存在")
        else:
            print(f"⚠️ {file} 缺失（可选）")
            
    # 检查data目录
    if not os.path.exists('data'):
        os.makedirs('data', exist_ok=True)
        print("✅ data 目录已创建")
    else:
        print("✅ data 目录存在")
        
    return all_good


def main():
    """主测试函数"""
    print("🚀 AI API 系统兼容性测试")
    print("=" * 60)
    
    tests = [
        ("文件结构检查", test_file_structure),
        ("模块导入测试", test_imports),
        ("配置加载测试", test_config_loading),
        ("API调用结构测试", test_api_call_structure),
        ("ai_analysis.py兼容性", test_ai_analysis_compatibility),
        ("html_generator.py兼容性", test_html_generator_compatibility),
        ("模拟API调用测试", test_mock_api_call)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统兼容性良好")
        print("\n💡 建议:")
        print("  - 可以安全运行 python main.py")
        print("  - 系统已配置为使用 Google Gemini API")
        print("  - 所有现有调用方式保持兼容")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查上述问题")
        print("\n💡 建议:")
        print("  - 检查缺失的文件")
        print("  - 验证配置文件格式")
        print("  - 确保所有依赖已安装")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
