# -*- coding: utf-8 -*-
"""
统一模型配置管理
支持为不同步骤和用户层级配置不同的AI模型
"""

from openai import OpenAI
import json
import os
from typing import Dict, Optional, Any


class ModelConfig:
    """模型配置管理类"""
    
    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.clients = {}  # 缓存不同的客户端实例
        
    def _load_config(self) -> Dict[str, Any]:
        """加载模型配置"""
        default_config = {
            "providers": {
                "openrouter": {
                    "api_key": "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",
                    "base_url": "https://openrouter.ai/api/v1"
                },
                "gemini": {
                    "api_key": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
                    "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"
                }
            },
            "models": {
                "step1_analysis": {
                    "provider": "gemini",
                    "model": "gemini-2.5-flash-preview-05-20",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95,
                    "reasoning_effort": "medium"
                },
                "step2_html_generation": {
                    "provider": "gemini",
                    "model": "gemini-2.5-pro-preview-06-05",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_configs(default_config, config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self._save_config(default_config)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            return default_config
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_client(self, provider: str) -> OpenAI:
        """获取指定提供商的OpenAI客户端"""
        if provider not in self.clients:
            provider_config = self.config["providers"][provider]
            self.clients[provider] = OpenAI(
                api_key=provider_config["api_key"],
                base_url=provider_config["base_url"],
                timeout=120.0  # 设置2分钟超时
            )
        
        return self.clients[provider]
    
    def get_model_params(self, step: str) -> Dict[str, Any]:
        """获取指定步骤的模型参数"""
        if step not in self.config["models"]:
            raise ValueError(f"未找到步骤 '{step}' 的配置")
        
        return self.config["models"][step].copy()
    
    def call_api(self, step: str, messages: list, max_retries: int = 3) -> Optional[str]:
        """统一的API调用接口"""
        params = self.get_model_params(step)
        provider = params.get("provider", "gemini")
        client = self.get_client(provider)
        
        # 从参数中提取OpenAI API需要的参数
        api_params = {
            "model": params["model"],
            "messages": messages,
            "temperature": params.get("temperature", 0.3),
            "max_tokens": params.get("max_tokens", 8000),
            "top_p": params.get("top_p", 0.95)
        }
        
        # OpenRouter不支持reasoning_effort参数，只有直连Gemini支持
        if "reasoning_effort" in params and provider == "gemini":
            api_params["reasoning_effort"] = params["reasoning_effort"]
        
        import time
        
        for attempt in range(max_retries):
            try:
                model = params["model"]
                print(f"🤖 调用 {model} API (步骤:{step}, 尝试 {attempt + 1}/{max_retries})...")
                
                # 添加超时时间到API调用
                import signal
                
                def timeout_handler(signum, frame):
                    raise TimeoutError("API调用超时")
                
                # 设置信号处理器（Windows可能不支持，作为backup）
                try:
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(180)  # 3分钟超时
                except:
                    pass  # Windows系统忽略signal设置
                
                # 添加流式输出参数
                api_params["stream"] = True
                
                response = client.chat.completions.create(**api_params)
                
                # 取消超时
                try:
                    signal.alarm(0)
                except:
                    pass
                
                # 处理流式响应 - 智能超时机制（不打印内容）
                full_response = ""
                print("📝 AI响应处理中...", end="", flush=True)
                
                import time
                response_start_time = time.time()  # 记录响应开始时间
                total_timeout = 600  # 10分钟总超时（给thinking充足时间）
                last_content_time = time.time()  # 最后接收内容时间
                content_timeout = 60  # 60秒无新内容超时（内容传输阶段）
                
                # 思考阶段进度指示器
                thinking_dots = 0
                last_thinking_update = time.time()
                thinking_interval = 10  # 每10秒更新一次思考进度
                thinking_warned = False  # 是否已警告长时间思考
                content_started = False  # 是否已开始接收内容
                
                try:
                    chunk_count = 0
                    for chunk in response:
                        chunk_count += 1
                        current_time = time.time()
                        elapsed = int(current_time - response_start_time)
                        
                        # 检查总响应时间是否超时（10分钟兜底）
                        if current_time - response_start_time > total_timeout:
                            print(f"\n⏰ 总响应超时 ({total_timeout//60}分钟)，可能API无响应，中断并重试...")
                            raise TimeoutError(f"总响应超时({total_timeout//60}分钟)")
                        
                        # 检查内容接收超时（仅在已经开始接收内容后检查）
                        if full_response and current_time - last_content_time > content_timeout:
                            print(f"\n⏰ 内容传输中断 ({content_timeout}秒无新内容)，重试...")
                            raise TimeoutError(f"内容传输超时({content_timeout}秒)")
                        
                        # 如果还没有收到内容，显示thinking进度
                        if not content_started:
                            # 长时间思考警告（只显示一次）
                            if elapsed > 180 and not thinking_warned:  # 3分钟后警告
                                print(f"\n💭 AI正在深度思考中，这可能需要几分钟时间，请耐心等待...")
                                thinking_warned = True
                            
                            # 定期更新思考进度
                            if current_time - last_thinking_update > thinking_interval:
                                thinking_dots = (thinking_dots + 1) % 4
                                dots = "." * thinking_dots + " " * (3 - thinking_dots)
                                
                                # 显示更详细的进度信息
                                if elapsed < 60:
                                    print(f"\r📝 AI思考中{dots} ({elapsed}s)", end="", flush=True)
                                else:
                                    minutes = elapsed // 60
                                    seconds = elapsed % 60
                                    print(f"\r📝 AI深度思考中{dots} ({minutes}m{seconds}s)", end="", flush=True)
                                
                                last_thinking_update = current_time
                        
                        # 处理chunk数据
                        if chunk.choices and len(chunk.choices) > 0:
                            choice = chunk.choices[0]
                            
                            # 检查是否有delta内容
                            if hasattr(choice, 'delta') and choice.delta:
                                delta_content = getattr(choice.delta, 'content', None)
                                if delta_content:
                                    # 如果这是第一个内容，显示开始接收
                                    if not content_started:
                                        elapsed_final = int(current_time - response_start_time)
                                        if elapsed_final < 60:
                                            print(f"\r✨ AI思考完成({elapsed_final}s)，正在接收响应...")
                                        else:
                                            minutes = elapsed_final // 60
                                            seconds = elapsed_final % 60
                                            print(f"\r✨ AI思考完成({minutes}m{seconds}s)，正在接收响应...")
                                        content_started = True
                                    
                                    # 收集响应内容但不打印
                                    full_response += delta_content
                                    last_content_time = current_time  # 更新最后接收内容的时间
                            
                            # 检查是否有finish_reason
                            if hasattr(choice, 'finish_reason') and choice.finish_reason:
                                if choice.finish_reason == 'stop':
                                    break  # 正常完成
                                elif choice.finish_reason == 'length':
                                    print(f"\n⚠️ 响应因长度限制而截断")
                                    break
                                elif choice.finish_reason == 'content_filter':
                                    print(f"\n⚠️ 响应被内容过滤器拦截")
                                    break
                                else:
                                    print(f"\n⚠️ 响应异常结束: {choice.finish_reason}")
                                    break
                    
                    print(f"\n🔍 调试信息: 处理了 {chunk_count} 个chunk")
                
                except Exception as stream_error:
                    # 流式响应异常，重新抛出以触发重试
                    elapsed = int(time.time() - response_start_time)
                    print(f"\n❌ 流式响应异常(耗时{elapsed}s): {stream_error}")
                    # 添加更详细的异常信息
                    import traceback
                    print(f"🔍 异常详情: {traceback.format_exc()}")
                    raise stream_error
                
                # 响应完成，显示最终状态
                if full_response:
                    total_elapsed = int(time.time() - response_start_time)
                    if total_elapsed < 60:
                        print(f"\r✅ 响应完成({total_elapsed}s)，内容长度: {len(full_response)} 字符        ")
                    else:
                        minutes = total_elapsed // 60
                        seconds = total_elapsed % 60
                        print(f"\r✅ 响应完成({minutes}m{seconds}s)，内容长度: {len(full_response)} 字符        ")
                else:
                    total_elapsed = int(time.time() - response_start_time)
                    print(f"\r❌ 响应为空 (处理{chunk_count}个chunk, 耗时{total_elapsed}s)                    ")
                    
                    # 如果是流式响应为空，尝试非流式调用作为备用方案
                    if chunk_count > 0:  # 确实接收到了chunk但内容为空
                        print(f"🔄 尝试非流式调用作为备用方案...")
                        try:
                            # 移除stream参数，使用非流式调用
                            api_params_backup = api_params.copy()
                            api_params_backup["stream"] = False
                            
                            backup_response = client.chat.completions.create(**api_params_backup)
                            
                            if backup_response.choices and len(backup_response.choices) > 0:
                                backup_content = backup_response.choices[0].message.content
                                if backup_content:
                                    print(f"✅ 非流式备用调用成功，内容长度: {len(backup_content)} 字符")
                                    return backup_content.strip()
                                else:
                                    print(f"❌ 非流式调用也返回空内容")
                            else:
                                print(f"❌ 非流式调用没有返回有效choices")
                        except Exception as backup_error:
                            print(f"❌ 非流式备用调用失败: {backup_error}")
                
                print()  # 换行
                
                if full_response:
                    print(f"✅ API调用成功，响应长度: {len(full_response)} 字符")
                    return full_response.strip()
                else:
                    print(f"❌ API返回空响应 (可能的原因: 网络问题、API限制、内容过滤)")
                    # 继续重试而不是立即返回None
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ API调用失败 (尝试 {attempt + 1}/{max_retries}): {error_str}")
                
                # 检查是否为OpenRouter的token超限错误
                if "maximum context length" in error_str and "tokens" in error_str:
                    # 提取OpenRouter格式的token信息并返回标准格式
                    import re
                    # OpenRouter格式: "you requested about 1095078 tokens (1029542 of text input, 65536 in the output)"
                    # 最大限制: "maximum context length is 1048576 tokens"
                    
                    current_match = re.search(r'you requested about (\d+) tokens', error_str)
                    max_match = re.search(r'maximum context length is (\d+) tokens', error_str)
                    
                    if current_match and max_match:
                        current_tokens = int(current_match.group(1))
                        max_tokens = int(max_match.group(1))
                        print(f"🎯 检测到OpenRouter token超限: 当前{current_tokens:,}, 最大{max_tokens:,}")
                        return f"TOKEN_LIMIT_EXCEEDED:{current_tokens}:{max_tokens}"
                
                if attempt < max_retries - 1:
                    # 根据错误类型确定等待时间
                    if "429" in error_str or "rate limit" in error_str.lower():
                        wait_time = 60  # 固定等待1分钟
                        print(f"⏰ 检测到速率限制，等待 {wait_time} 秒后重试...")
                    elif "503" in error_str or "502" in error_str or "server" in error_str.lower():
                        wait_time = min(30, 5 * (2 ** attempt))
                        print(f"⏰ 检测到服务器错误，等待 {wait_time} 秒后重试...")
                    elif "connection" in error_str.lower() or "timeout" in error_str.lower():
                        wait_time = min(15, 3 * (2 ** attempt))
                        print(f"⏰ 检测到连接错误，等待 {wait_time} 秒后重试...")
                    else:
                        wait_time = min(20, 2 ** attempt)
                        print(f"⏰ 其他错误，等待 {wait_time} 秒后重试...")
                    
                    time.sleep(wait_time)
                else:
                    print(f"❌ API调用最终失败，已达到最大重试次数")
        
        return None
    
    def update_config(self, step: str, params: Dict[str, Any]):
        """更新配置"""
        self.config["models"][step] = params
        self._save_config(self.config)
        print(f"✅ 已更新配置: {step}")
    
    def list_configs(self):
        """列出所有配置"""
        print("📋 当前模型配置:")
        for step, step_config in self.config["models"].items():
            model = step_config.get("model", "未设置")
            max_tokens = step_config.get("max_tokens", "未设置")
            reasoning = step_config.get("reasoning_effort", "N/A")
            print(f"  {step}: {model} (max_tokens: {max_tokens}, reasoning_effort: {reasoning})")


# 全局配置实例
model_config = ModelConfig()


def get_model_config() -> ModelConfig:
    """获取全局模型配置实例"""
    return model_config 