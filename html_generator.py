# -*- coding: utf-8 -*-
"""HTML生成脚本 - 使用AI分析结果和prompt2生成HTML网页代码"""

import os
import json
import time
from datetime import datetime, timedelta
from SET import Setting
from TA import Ta
from model_config import get_model_config

# 获取模型配置管理器
model_config = get_model_config()

def load_prompt2_template():
    """从prompt2.md文件加载提示词模板"""
    try:
        with open('prompt2.md', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print("❌ 错误: 未找到prompt2.md文件")
        return None

def load_ai_analysis_json(file_path):
    """加载AI分析结果JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"❌ 错误: 未找到文件 {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ 错误: JSON文件格式有误 - {e}")
        return None

def call_gemini_api(messages, max_retries=3):
    """调用Gemini API，使用统一模型配置"""
    return model_config.call_api(
        step="step2_html_generation",
        messages=messages,
        max_retries=max_retries
    )

def clean_html_response(response):
    """清理AI返回的HTML响应，确保只保留纯HTML代码"""
    if not response:
        return None
    
    # 先按行分割
    lines = response.split('\n')
    
    # 找到HTML开始的位置
    html_start_index = -1
    for i, line in enumerate(lines):
        # 查找 <!DOCTYPE html> 或 <html 标签
        if '<!DOCTYPE html>' in line or line.strip().startswith('<html'):
            html_start_index = i
            break
    
    # 如果没找到HTML开始标记，返回None
    if html_start_index == -1:
        print("❌ 警告: 未找到有效的HTML开始标记")
        return None
    
    # 从HTML开始位置截取内容
    html_lines = lines[html_start_index:]
    
    # 清理每一行，去掉可能的markdown标记
    cleaned_lines = []
    skip_line = False
    
    for line in html_lines:
        line_stripped = line.strip()
        
        # 跳过markdown代码块标记
        if line_stripped in ['```html', '```HTML', '```']:
            skip_line = not skip_line  # 切换跳过状态
            continue
        
        # 如果不在跳过状态，保留该行
        if not skip_line:
            cleaned_lines.append(line)
    
    # 合并清理后的内容
    cleaned_content = '\n'.join(cleaned_lines)
    
    # 验证结果
    if not cleaned_content.strip().startswith('<!DOCTYPE html>'):
        print("❌ 警告: 清理后的内容不是有效的HTML")
        return None
    
    return cleaned_content.strip()

def get_yesterday_date():
    """获取昨日日期，格式为YYYY-MM-DD"""
    yesterday = datetime.now() - timedelta(days=1)
    return yesterday.strftime('%Y-%m-%d')

def find_latest_analysis_file():
    """查找最新的AI分析结果文件"""
    # 查找data目录下的ai_analysis_*.json文件
    data_dir = 'data'
    if not os.path.exists(data_dir):
        return None
        
    analysis_files = [f for f in os.listdir(data_dir) if f.startswith('ai_analysis_') and f.endswith('.json')]
    
    if not analysis_files:
        return None
    
    # 按文件名排序，取最新的，返回完整路径
    analysis_files.sort(reverse=True)
    return os.path.join(data_dir, analysis_files[0])

def get_global_unique_users_count(target_date):
    """从数据库直接查询全局唯一用户数（同时覆盖D版和E版）"""
    print(f"🔍 查询 {target_date} 的全局唯一用户数...")
    
    try:
        # 查询D版和E版的所有唯一用户
        unique_users_set = set()
        
        for game_version in ['D', 'E']:
            try:
                ta = Ta(game_version)
                
                # 根据游戏版本使用不同的SQL查询
                if game_version == 'E':
                    # E版查询
                    sql = f"""
                    SELECT DISTINCT u.role_name
                    FROM v_event_24 ev
                    INNER JOIN v_user_24 u ON ev."#user_id" = u."#user_id"
                    WHERE ev."$part_event" = 'role_chat'
                        AND ev."$part_date" = '{target_date}'
                        AND u."#event_date" > 20250529
                        AND u.role_name IS NOT NULL 
                        AND u.role_name != ''
                        AND LENGTH(TRIM(u.role_name)) > 0
                    """
                else:
                    # D版查询
                    sql = f"""
                    SELECT DISTINCT u.role_name
                    FROM v_event_2 ev
                    INNER JOIN v_user_2 u ON ev."#user_id" = u."#user_id"
                    WHERE ev."$part_event" = 'Chat'
                        AND ev."$part_date" = '{target_date}'
                        AND u."#event_date" > 20250529
                        AND u.role_name IS NOT NULL 
                        AND u.role_name != ''
                        AND LENGTH(TRIM(u.role_name)) > 0
                    """
                
                result = ta.sql_query(sql)
                
                if result is not None and not result.empty:
                    # 将角色名添加到全局set中去重
                    version_users = set(result['role_name'].dropna().unique())
                    unique_users_set.update(version_users)
                    print(f"  ✅ {game_version}版: 查询到 {len(version_users)} 个唯一用户")
                else:
                    print(f"  ⚠️ {game_version}版: 查询结果为空")
                    
            except Exception as e:
                print(f"  ❌ {game_version}版查询失败: {e}")
                continue
        
        global_unique_count = len(unique_users_set)
        print(f"✅ 全局唯一用户数: {global_unique_count}")
        return global_unique_count
        
    except Exception as e:
        print(f"❌ 全局用户查询失败: {e}")
        return 0

def extract_data_summary(analysis_data):
    """从分析数据中提取关键信息摘要，包含充分的用户计算"""
    summary = {
        "total_users": 0,
        "total_messages": 0,
        "versions": {"E版": [], "D版": []},
        "user_levels": set(),
        "has_errors": False,
        "version_breakdown": {"E版": 0, "D版": 0},
        "level_breakdown": {},
        "global_unique_users": 0,
        "calculation_method": "simple_sum"  # 标记计算方法
    }

    print("📊 开始数据汇总计算...")

    # 收集各版本各层级的用户数
    for key, data in analysis_data.items():
        if "error" in data:
            summary["has_errors"] = True
            print(f"  ⚠️ {key}: 包含错误，跳过")
            continue

        metadata = data.get("metadata", {})
        unique_users = metadata.get("unique_users", 0)
        total_comments = metadata.get("total_comments", 0)

        print(f"  📈 {key}: {unique_users:,} 用户, {total_comments:,} 消息")

        summary["total_messages"] += total_comments

        # 版本分类统计
        if key.startswith("E版-"):
            version = "E版"
            level = key.replace("E版-", "")
            summary["versions"]["E版"].append(level)
            summary["version_breakdown"]["E版"] += unique_users
        elif key.startswith("D版-"):
            version = "D版"
            level = key.replace("D版-", "")
            summary["versions"]["D版"].append(level)
            summary["version_breakdown"]["D版"] += unique_users
        else:
            level = key
            version = "未知"

        # 层级统计
        summary["user_levels"].add(level)
        if level not in summary["level_breakdown"]:
            summary["level_breakdown"][level] = {"D版": 0, "E版": 0, "total": 0}

        if version in ["D版", "E版"]:
            summary["level_breakdown"][level][version] = unique_users
            summary["level_breakdown"][level]["total"] += unique_users

    # 简单累加方式（可能重复计算）
    simple_sum_users = sum(summary["version_breakdown"].values())
    summary["total_users"] = simple_sum_users

    print(f"📊 初步统计结果:")
    print(f"  💬 总消息数: {summary['total_messages']:,}")
    print(f"  👥 简单累加用户数: {simple_sum_users:,}")
    print(f"  🎮 D版用户: {summary['version_breakdown']['D版']:,}")
    print(f"  🎮 E版用户: {summary['version_breakdown']['E版']:,}")

    # 尝试查询真实的全局唯一用户数
    yesterday = get_yesterday_date()
    print(f"🔍 尝试查询 {yesterday} 的全局唯一用户数...")
    global_query_result = get_global_unique_users_count(yesterday)

    if global_query_result > 0:
        summary["global_unique_users"] = global_query_result
        summary["calculation_method"] = "database_query"
        # 使用更准确的数据库查询结果
        summary["total_users"] = summary["global_unique_users"]
        print(f"✅ 使用数据库查询结果: {global_query_result:,} 用户")
    else:
        # 如果数据库查询失败，使用估算方法
        estimated_unique = estimate_unique_users(summary["version_breakdown"])
        summary["global_unique_users"] = estimated_unique
        summary["calculation_method"] = "estimated"
        summary["total_users"] = estimated_unique
        print(f"📊 数据库查询失败，使用估算结果: {estimated_unique:,} 用户")
        print(f"   (假设版本间重复率20%，从 {simple_sum_users:,} 估算为 {estimated_unique:,})")

    return summary

def estimate_unique_users(version_breakdown):
    """估算全局唯一用户数，考虑版本间重复率"""
    d_users = version_breakdown.get("D版", 0)
    e_users = version_breakdown.get("E版", 0)
    
    if d_users == 0:
        return e_users
    if e_users == 0:
        return d_users
    
    # 假设版本间重复率为15-25%（基于经验估算）
    # 使用保守估算：重复率20%
    overlap_rate = 0.20
    smaller_version = min(d_users, e_users)
    estimated_overlap = smaller_version * overlap_rate
    
    estimated_unique = d_users + e_users - estimated_overlap
    return int(estimated_unique)

def post_process_html(html_content):
    """后处理HTML内容，确保结构完整"""
    if not html_content:
        return None
    
    # 检查是否有完整的结束标签
    has_script_end = '</script>' in html_content
    has_body_end = '</body>' in html_content  
    has_html_end = '</html>' in html_content
    
    # 如果缺少结束标签，添加它们
    if not has_script_end and '<script>' in html_content:
        # 查找最后一个script开始标签的位置
        script_start = html_content.rfind('<script>')
        if script_start > -1:
            # 在最后添加script结束标签
            html_content += '\n    </script>'
    
    if not has_body_end:
        html_content += '\n</body>'
    
    if not has_html_end:
        html_content += '\n</html>'
    
    return html_content

def main():
    """主函数 - 生成HTML网页"""
    print("=== HTML生成系统 ===")
    
    # 1. 加载prompt2模板
    print("📖 加载prompt2模板...")
    prompt2_template = load_prompt2_template()
    if not prompt2_template:
        return
    
    # 2. 查找并加载最新的AI分析结果
    print("🔍 查找最新的AI分析结果...")
    analysis_file = find_latest_analysis_file()
    if not analysis_file:
        print("❌ 错误: 未找到AI分析结果文件")
        return
        
    print(f"📁 找到分析文件: {analysis_file}")
    
    analysis_data = load_ai_analysis_json(analysis_file)
    if not analysis_data:
        return
    
    # 3. 提取数据摘要（使用改进的计算方法）
    print("📊 分析数据概况（使用充分计算）...")
    data_summary = extract_data_summary(analysis_data)
    
    # 显示详细的用户统计信息
    print(f"  📈 活跃用户数: {data_summary['total_users']:,} （{data_summary['calculation_method']}）")
    if data_summary['calculation_method'] == 'database_query':
        print(f"    ✅ 基于数据库查询的准确统计")
    elif data_summary['calculation_method'] == 'estimated':
        print(f"    📊 基于估算的去重统计（考虑版本重复率）")
        print(f"    📊 D版用户: {data_summary['version_breakdown']['D版']:,}")
        print(f"    📊 E版用户: {data_summary['version_breakdown']['E版']:,}")
        print(f"    📊 原始累加: {sum(data_summary['version_breakdown'].values()):,}")
    else:
        print(f"    ⚠️ 简单累加统计（可能包含重复）")
    
    print(f"  💬 总消息数: {data_summary['total_messages']:,}")
    print(f"  🎮 版本: {list(data_summary['versions'].keys())}")
    print(f"  👥 用户层级: {sorted(data_summary['user_levels'])}")
    
    # 显示各层级用户分布
    print(f"\n📊 各层级用户分布:")
    for level, breakdown in data_summary['level_breakdown'].items():
        print(f"    {level}: D版 {breakdown['D版']:,} + E版 {breakdown['E版']:,} = {breakdown['total']:,}")
    
    if data_summary["has_errors"]:
        print("⚠️  注意: 部分数据分析出现错误")
    
    # 4. 准备AI请求
    print("🤖 准备生成HTML...")
    
    # 构建完整的数据JSON字符串
    analysis_json_str = json.dumps(analysis_data, ensure_ascii=False, indent=2)
    
    # 构建AI对话消息
    messages = [
        {
            "role": "system",
            "content": prompt2_template
        },
        {
            "role": "user",
            "content": f"""根据以下JSON格式的游戏舆情分析数据，生成完整的HTML可视化日报。

请严格按照prompt2.md中的规则处理数据，生成包含完整HTML结构、CSS样式和数据内容的网页代码。

分析数据:
```json
{analysis_json_str}
```

请生成完整的HTML代码，包括:
1. HTML文档结构 (<!DOCTYPE html>, <html>, <head>, <body>)
2. 内嵌CSS样式
3. 根据数据填充的所有占位符内容
4. 响应式设计，适配移动端和桌面端

确保所有{{变量名}}占位符都被正确替换为实际数据。"""
        }
    ]
    
    # 5. 调用AI生成HTML
    print("🚀 调用AI生成HTML...")
    start_time = time.time()
    
    raw_html_content = call_gemini_api(messages)
    
    if raw_html_content:
        # 6. 清理AI返回的HTML响应
        print("🧹 清理HTML响应...")
        html_content = clean_html_response(raw_html_content)
        
        if not html_content:
            print("❌ HTML清理失败，使用原始内容")
            html_content = raw_html_content
        
        # 7. 后处理HTML确保结构完整
        print("🔧 后处理HTML结构...")
        html_content = post_process_html(html_content)
        
        if html_content:
            # 确保data目录存在
            os.makedirs('data', exist_ok=True)
            
            # 8. 保存HTML文件
            yesterday = get_yesterday_date()
            output_file = f"data/game_sentiment_report_{yesterday}.html"
            
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                
                end_time = time.time()
                print(f"\n{'='*60}")
                print(f"✅ HTML生成成功!")
                print(f"📁 文件已保存至: {output_file}")
                print(f"📊 基于数据文件: {analysis_file}")
                print(f"⏱️ 生成耗时: {end_time - start_time:.1f} 秒")
                print(f"📄 HTML文件大小: {len(html_content):,} 字符")
                print(f"{'='*60}")
                
                # 显示HTML预览信息
                print(f"\n📋 HTML内容预览:")
                print(f"{'='*40}")
                preview = html_content[:500] + "..." if len(html_content) > 500 else html_content
                print(preview)
                print(f"{'='*40}")
                
            except Exception as e:
                print(f"❌ 保存HTML文件失败: {e}")
        else:
            print("❌ HTML后处理失败")
    else:
        print("❌ HTML生成失败")

if __name__ == "__main__":
    main() 