---
description: 
globs: 
alwaysApply: false
---
# 游戏舆情分析系统项目规则

## 1. 文件组织结构
- 所有生成的文件（JSON、HTML、JPG）统一存放在 `data/` 子文件夹中
- 原始数据文件格式：`data/raw_data_{版本}版_{用户层级}_{日期}.json`
- AI分析结果：`data/ai_analysis_{日期}.json`
- HTML报告：`data/game_sentiment_report_{日期}.html`
- 图片报告：`data/game_sentiment_report_{日期}_screenshot.jpg`

## 2. 并发控制
- AI分析并发数限制为3条，防止API请求限制
- 使用ThreadPoolExecutor(max_workers=3)

## 3. 企业微信推送配置
- 推送地址：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c453c302-149e-4178-9ffd-e79cb25bbd37
- 推送策略：仅推送图片，使用base64编码和md5验证
- 图片大小限制：2MB以内

## 4. 游戏版本和用户层级
- 游戏版本：D版、E版
- 用户层级：零氪用户、小R用户、中R用户、大R用户、超R用户
- D版零氪用户条件：tag_value = '非R'
- E版零氪用户条件：tag_value IS NULL

## 5. 技术栈
- 数据库查询：自定义TA模块
- AI分析：Gemini API
- HTML生成：AI生成 + 模板
- 图片转换：Playwright + PIL
- 企业微信推送：requests + base64/md5

## 6. 错误处理
- API调用失败时进行重试（最多3次）
- 文件操作失败时提供详细错误信息
- 支持部分失败的情况下继续执行后续步骤

## 7. 性能优化
- 智能采样：每个层级最多分析1000条言论
- 图片压缩：自动优化图片大小在2MB以内
- 并发控制：避免同时过多API请求

## 8. 数据处理
- 日期格式：HTML模板中使用"YYYY年MM月DD日"格式
- 数据去重：metadata中的数字已经是去重后的统计
- 统计汇总：总消息数和总用户数通过各层级数据相加得出
- 明确数据传递：向AI明确传递格式化的日期和统计数据
