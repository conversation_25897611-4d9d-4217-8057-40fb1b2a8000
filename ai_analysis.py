# -*- coding: utf-8 -*-
"""AI言论分析脚本 - 使用Gemini API对用户言论进行情绪和话题分析"""

import datetime
import json
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import requests
from TA import Ta
from model_config import get_model_config

# 重试配置
SQL_RETRY_MAX = 3            # SQL查询最大重试次数
SQL_RETRY_DELAY = 5          # SQL查询重试间隔（秒）
TASK_RETRY_DELAY = 60        # 任务级重试前等待时间（秒）
FAILED_TASK_RETRY_DELAY = 60 # 失败任务重试间隔（秒）
LEVEL_ANALYSIS_DELAY = 5     # 每个层级分析间隔（秒）- 正常任务间短间隔
CONCURRENT_WORKERS = 1       # 串行处理，确保每个层级完成后再进入下一个

# Token限制配置
MAX_INPUT_TOKENS = 1000000   # API输入token最大限制（OpenRouter为1048576，设置安全余量）
APPROX_CHARS_PER_TOKEN = 3   # 估算的每个token平均字符数（中文约3-4字符/token）

# 尝试导入tiktoken用于精确token计算
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    # 使用与Gemini兼容的编码器（cl100k_base是通用的GPT编码器）
    TIKTOKEN_ENCODING = tiktoken.get_encoding("cl100k_base")
    print("✅ 已启用tiktoken精确token计算")
except ImportError:
    TIKTOKEN_AVAILABLE = False
    TIKTOKEN_ENCODING = None
    print("⚠️  tiktoken未安装，使用近似token估算方法")
    print("💡 提示：运行 'pip install tiktoken' 可启用精确token计算")

# 获取模型配置管理器
model_config = get_model_config()

def estimate_token_count(text):
    """估算文本的token数量（优先使用tiktoken精确计算，fallback到近似估算）"""
    if TIKTOKEN_AVAILABLE and TIKTOKEN_ENCODING:
        try:
            # 使用tiktoken进行精确token计算
            return len(TIKTOKEN_ENCODING.encode(text))
        except Exception as e:
            print(f"⚠️ tiktoken计算失败，使用fallback方法: {e}")
    
    # Fallback到近似估算方法
    # 区分中文和英文字符进行更精确的估算
    chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
    other_chars = len(text) - chinese_chars
    
    # 中文字符：每个字符约1个token
    # 英文字符：每3-4个字符约1个token
    estimated_tokens = chinese_chars + (other_chars // APPROX_CHARS_PER_TOKEN)
    
    # 确保至少有1个token（避免除法结果为0）
    return max(1, estimated_tokens)

def truncate_data_by_tokens(csv_lines, system_prompt, max_tokens=MAX_INPUT_TOKENS):
    """根据token限制截断CSV数据"""
    # 计算系统提示词和基础消息的token数
    base_message_template = "请分析以下{level_name}的言论数据（共{count}条，CSV格式，表头为：言论内容,角色名）：\n\n"
    base_tokens = estimate_token_count(system_prompt) + estimate_token_count(base_message_template)
    
    # 为基础消息预留token空间
    available_tokens = max_tokens - base_tokens - 1000  # 预留1000 token的安全余量
    
    if available_tokens <= 0:
        print("⚠️ 系统提示词过长，无法处理数据")
        return csv_lines[:2], 1, len(csv_lines) - 1  # 只返回表头和一行数据
    
    # CSV表头
    csv_header = csv_lines[0]
    data_lines = csv_lines[1:]
    
    # 逐行添加数据，直到接近token限制
    selected_lines = [csv_header]
    current_tokens = estimate_token_count(csv_header)
    
    for line in data_lines:
        line_tokens = estimate_token_count(line)
        if current_tokens + line_tokens > available_tokens:
            break
        selected_lines.append(line)
        current_tokens += line_tokens
    
    original_count = len(data_lines)  # 原始数据行数（不包括表头）
    selected_count = len(selected_lines) - 1  # 选中的数据行数（不包括表头）
    
    if selected_count < original_count:
        calculation_method = "精确计算" if TIKTOKEN_AVAILABLE else "估算"
        print(f"⚠️  数据截断: 原始 {original_count} 条 → 截断至 {selected_count} 条 ({calculation_method}token: {current_tokens:,}/{max_tokens:,})")
    else:
        calculation_method = "精确计算" if TIKTOKEN_AVAILABLE else "估算"
        print(f"✅ 数据完整: {selected_count} 条 ({calculation_method}token: {current_tokens:,}/{max_tokens:,})")
    
    return selected_lines, selected_count, original_count

def load_prompt_template():
    """从prompt1.md文件加载提示词模板"""
    try:
        with open('prompt1.md', 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except FileNotFoundError:
        print("❌ 错误: 未找到prompt1.md文件")
        return None

def get_yesterday_date():
    """获取昨日日期，格式为YYYY-MM-DD"""
    yesterday = datetime.datetime.now() - datetime.timedelta(days=1)
    return yesterday.strftime('%Y-%m-%d')

def get_sql_template(condition, game_version):
    """根据付费层级条件和游戏版本生成对应的SQL查询模板"""
    
    # E版的SQL查询 - 使用与daily_comments.py相同的查询
    if game_version == 'E':
        base_sql = """
        SELECT 
            ev.chat_content AS "言论内容",
            u.role_name AS "角色名"
        FROM v_event_24 ev
        INNER JOIN v_user_24 u ON ev."#user_id" = u."#user_id"
        LEFT JOIN user_result_cluster_24 uc ON ev."#user_id" = uc."#user_id" 
            AND uc.cluster_name = 'tag_20241218_1'
        WHERE ev."$part_event" = 'role_chat'
            AND ev."$part_date" = '{}'
            AND u."#event_date" > 20250529
            AND strpos(ev.chat_content, '[:') = 0
            AND strpos(ev.chat_content, 'color=') = 0
            AND LENGTH(ev.chat_content) > 3
            AND LENGTH(ev.chat_content) < 33
            {}
        GROUP BY ev.chat_content, uc.tag_value, u.role_name
        ORDER BY COUNT(DISTINCT ev."#user_id") DESC
        """
    else:  # D版的SQL查询 - 使用与daily_comments_d.py相同的查询
        base_sql = """
        SELECT 
            ev.content AS "言论内容",
            u.role_name AS "角色名"
        FROM v_event_2 ev
        INNER JOIN v_user_2 u ON ev."#user_id" = u."#user_id"
        LEFT JOIN user_result_cluster_2 uc ON ev."#user_id" = uc."#user_id" 
            AND uc.cluster_name = 'tag_20241211_1'
        WHERE ev."$part_event" = 'Chat'
            AND ev."$part_date" = '{}'
            AND u."#event_date" > 20250529
            AND POSITION('[:' IN ev.content) = 0
            AND POSITION('4297|P|' IN ev.content) = 0
            AND POSITION('color=' IN ev.content) = 0
            AND LENGTH(ev.content) > 3
            AND LENGTH(ev.content) < 33
            {}
        GROUP BY ev.content, uc.tag_value, u.role_name
        ORDER BY COUNT(DISTINCT ev."#user_id") DESC
        """
    
    # 格式化SQL，注意这里使用两层格式化
    return base_sql.format('{}', condition)

def extract_token_info_from_error(error_str):
    """从API错误信息中提取token使用信息
    返回: (是否token超限错误, 当前token数, 最大token数)
    """
    import re
    
    error_lower = error_str.lower()
    
    # 检查是否为token超限错误
    token_limit_keywords = [
        "token count", "token limit", "exceeds the maximum", 
        "too many tokens", "context length", "maximum number of tokens",
        "input token count", "input too long", "current tokens"
    ]
    
    is_token_error = any(keyword in error_lower for keyword in token_limit_keywords)
    
    if not is_token_error:
        return False, None, None
    
    # 尝试提取具体的token数量
    # 常见格式：
    # "The input token count (1093777) exceeds the maximum number of tokens allowed (1048576)"
    # "Input too long: 1093777 tokens, maximum 1048576"
    # OpenRouter格式: "you requested about 1095078 tokens... maximum context length is 1048576 tokens"
    
    patterns = [
        r'input token count \((\d+)\).*?maximum.*?\((\d+)\)',  # 标准格式
        r'input too long.*?(\d+) tokens.*?maximum (\d+)',  # "Input too long: 1093777 tokens, maximum 1048576"
        r'you requested about (\d+) tokens.*?maximum context length is (\d+) tokens',  # OpenRouter格式
        r'token count.*?(\d+).*?limit.*?(\d+)',  # 通用格式
        r'current tokens.*?(\d+).*?max.*?(\d+)',  # "Current tokens: 950000, max allowed: 1048576"
        r'(\d+).*?exceeds.*?(\d+)',  # exceeds格式
        r'(\d+) tokens.*?maximum (\d+)',  # "1093777 tokens, maximum 1048576"
        r'tokens.*?(\d+).*?max.*?(\d+)',  # "tokens: XXX, max: YYY"
    ]
    
    for pattern in patterns:
        match = re.search(pattern, error_lower)
        if match:
            current_tokens = int(match.group(1))
            max_tokens = int(match.group(2))
            print(f"🎯 从API错误中提取token信息: 当前{current_tokens:,}, 最大{max_tokens:,}")
            return True, current_tokens, max_tokens
    
    # 如果无法提取具体数字，至少确认是token错误
    print(f"🔍 检测到token超限错误，但无法提取具体数字")
    return True, None, None

def is_token_limit_error(error_str):
    """检测是否为token超限错误（保持向后兼容）"""
    is_error, _, _ = extract_token_info_from_error(error_str)
    return is_error

def call_gemini_api(messages, max_retries=3):
    """调用Gemini API - 使用统一模型配置系统"""
    # 使用新的模型配置系统进行API调用
    result = model_config.call_api(
        step="step1_analysis",
        messages=messages,
        max_retries=max_retries
    )
    
    if result and result.startswith("TOKEN_LIMIT_EXCEEDED"):
        # 保持原有的token限制处理逻辑
        return result
    elif result:
        # 清理响应内容，去掉markdown代码块标记
        if result.startswith('```json'):
            result = result[7:]  # 去掉 ```json
        if result.startswith('```'):
            result = result[3:]   # 去掉 ```
        if result.endswith('```'):
            result = result[:-3]  # 去掉结尾的 ```
        return result.strip()
    
    return None

def call_gemini_api_with_dynamic_reduction(messages, level_name, csv_lines, system_content, max_reduction_attempts=3):
    """调用Gemini API并支持动态token缩减"""
    current_csv_lines = csv_lines.copy()
    reduction_attempt = 0
    
    while reduction_attempt < max_reduction_attempts:
        # 构建当前消息
        if reduction_attempt > 0:
            # 使用当前已缩减的数据重新构建消息
            comments_text = "\n".join(current_csv_lines)
            current_count = len(current_csv_lines) - 1  # 减去表头
            
            current_messages = [
                {
                    "role": "system",
                    "content": system_content
                },
                {
                    "role": "user", 
                    "content": f"请分析以下{level_name}的言论数据（共{current_count}条，CSV格式，表头为：言论内容,角色名）：\n\n{comments_text}"
                }
            ]
        else:
            current_messages = messages
        
        # 调用API
        result = call_gemini_api(current_messages)
        
        # 检查结果类型
        if result is None:
            # API调用失败（可能是超时等非token问题）
            print(f"❌ API调用失败（可能为超时或连接问题），直接重试...")
            return None, None, None
        
        elif result.startswith("TOKEN_LIMIT_EXCEEDED"):
            reduction_attempt += 1
            
            if reduction_attempt >= max_reduction_attempts:
                print(f"❌ 已达到最大缩减次数 ({max_reduction_attempts})，无法继续缩减")
                return None, None, None
            
            # 检查是否有API提供的token信息
            if ":" in result:
                try:
                    parts = result.split(":")
                    if len(parts) == 3 and parts[0] == "TOKEN_LIMIT_EXCEEDED":
                        current_tokens = int(parts[1])
                        max_tokens = int(parts[2])
                        
                        # 使用API提供的精确token信息计算缩减比例
                        # 需要为输出token留出空间，从input token中减去
                        output_buffer = 65536  # 为输出预留的token
                        target_input_tokens = int((max_tokens - output_buffer) * 0.95)  # 目标：最大输入值的95%
                        
                        # 当前tokens包含了请求的总token数，需要减去输出部分来得到输入部分
                        estimated_input_tokens = current_tokens - output_buffer
                        reduction_ratio = target_input_tokens / estimated_input_tokens if estimated_input_tokens > 0 else 0.5
                        
                        print(f"🎯 基于API计算精确缩减: 当前输入~{estimated_input_tokens:,} → 目标{target_input_tokens:,} (比例{reduction_ratio:.1%})")
                        
                        # 根据token比例缩减数据
                        original_count = len(current_csv_lines) - 1  # 减去表头
                        new_count = max(1, int(original_count * reduction_ratio))
                        current_csv_lines = [current_csv_lines[0]] + current_csv_lines[1:new_count+1]
                        
                        print(f"📊 精确数据缩减: {original_count} → {new_count} 条 (基于API token计算)")
                        continue
                        
                except (ValueError, IndexError):
                    pass  # 解析失败，使用fallback方案
            
            # Fallback: 使用tiktoken估算缩减
            print(f"🔄 动态缩减 {reduction_attempt}/{max_reduction_attempts}: 使用tiktoken估算缩减")
            
            # 使用truncate_data_by_tokens进行智能缩减
            target_max_tokens = int(MAX_INPUT_TOKENS * (0.8 ** reduction_attempt))
            truncated_csv_lines, analyzed_count, original_count = truncate_data_by_tokens(
                current_csv_lines, system_content, target_max_tokens
            )
            current_csv_lines = truncated_csv_lines
            
            print(f"📊 tiktoken估算缩减: {original_count} → {analyzed_count} 条")
            continue
        
        else:
            # 成功获得结果
            if reduction_attempt > 0:
                final_count = len(current_csv_lines) - 1
                print(f"✅ 动态缩减成功: 最终使用 {final_count} 条数据完成分析")
                return result, final_count, len(csv_lines) - 1
            else:
                # 没有缩减，使用原始数据量
                original_count = len(csv_lines) - 1
                return result, original_count, original_count
    
    return None, None, None

def analyze_comments_for_level(level_name, condition, yesterday, game_version):
    """分析单个付费层级的言论数据 - 支持多线程并发"""
    import threading
    thread_id = threading.get_ident()
    print(f"\n🔄 [线程{thread_id}] 开始分析: {game_version}版-{level_name}")
    
    try:
        # 为每个线程创建独立的数据库查询客户端
        ta = Ta(game_version)
        
        # 生成SQL语句
        sql_template = get_sql_template(condition, game_version)
        sql = sql_template.format(yesterday)
        
        # SQL查询重试机制
        result = None
        sql_retry_count = 0
        
        while sql_retry_count < SQL_RETRY_MAX:
            print(f"🔍 [线程{thread_id}] {game_version}版-{level_name}: 执行SQL查询 (尝试 {sql_retry_count + 1}/{SQL_RETRY_MAX})")
            result = ta.sql_query(sql)
            
            if result is not None and not result.empty:
                print(f"📊 [线程{thread_id}] {game_version}版-{level_name}: 获得 {len(result)} 条言论数据")
                break
            else:
                sql_retry_count += 1
                if sql_retry_count < SQL_RETRY_MAX:
                    print(f"⚠️ [线程{thread_id}] {game_version}版-{level_name}: 查询结果为空，等待 {SQL_RETRY_DELAY} 秒后重试...")
                    time.sleep(SQL_RETRY_DELAY)
                else:
                    print(f"❌ [线程{thread_id}] {game_version}版-{level_name}: SQL查询最终失败，已达到最大重试次数 ({SQL_RETRY_MAX})")
        
        # 如果所有重试都失败，返回错误
        if result is None or result.empty:
            print(f"❌ [线程{thread_id}] {game_version}版-{level_name}: 查询结果为空 (已重试 {SQL_RETRY_MAX} 次)")
            return f"{game_version}版-{level_name}", {"error": f"查询结果为空 (已重试 {SQL_RETRY_MAX} 次)"}
        
        # 确保data目录存在（线程安全）
        os.makedirs('data', exist_ok=True)
        
        # 保存原始查询数据为JSON（添加线程标识避免冲突）
        raw_data_file = f"data/raw_data_{game_version}版_{level_name}_{yesterday}_t{thread_id}.json"
        result_json = result.to_json(orient='records', force_ascii=False, indent=2)
        with open(raw_data_file, 'w', encoding='utf-8') as f:
            f.write(result_json)
        print(f"💾 [线程{thread_id}] {game_version}版-{level_name}: 原始数据已保存至 {raw_data_file}")
        
        # 全量分析（不再限制条数）
        sample_size = len(result)
        sample_result = result
        
        # 准备AI分析的输入数据 - 使用CSV格式
        csv_header = "言论内容,角色名"
        csv_lines = [csv_header]
        for _, row in sample_result.iterrows():
            # 转义CSV中的逗号和引号
            content = str(row['言论内容']).replace('"', '""')
            role_name = str(row['角色名']).replace('"', '""')
            # 如果内容包含逗号，需要用引号包围
            if ',' in content:
                content = f'"{content}"'
            if ',' in role_name:
                role_name = f'"{role_name}"'
            csv_lines.append(f"{content},{role_name}")
        
        # 加载提示词模板
        prompt_template = load_prompt_template()
        if not prompt_template:
            return f"{game_version}版-{level_name}", {"error": "无法加载提示词模板"}
        
        # 直接使用完整数据，不进行预估算截断
        system_content = f"当前分析的用户层级：{level_name}\n\n{prompt_template}"
        comments_text = "\n".join(csv_lines)
        original_count = len(csv_lines) - 1  # 减去表头
        
        print(f"📊 [线程{thread_id}] {game_version}版-{level_name}: 使用完整数据 {original_count} 条进行分析")
        
        # 构建AI对话消息
        messages = [
            {
                "role": "system",
                "content": system_content
            },
            {
                "role": "user", 
                "content": f"请分析以下{level_name}的言论数据（共{original_count}条，CSV格式，表头为：言论内容,角色名）：\n\n{comments_text}"
            }
        ]
        
        # 调用AI API - 使用动态缩减API调用
        print(f"🤖 [线程{thread_id}] {game_version}版-{level_name}: 正在调用AI分析...")
        
        # 使用动态缩减API调用
        ai_response, final_analyzed_count, original_data_count = call_gemini_api_with_dynamic_reduction(
            messages, level_name, csv_lines, system_content
        )
        
        # 如果动态缩减失败，尝试多次传统重试（用于流式响应超时等问题）
        retry_count = 0
        max_retries = 5  # 增加重试次数
        
        while not ai_response and retry_count < max_retries:
            retry_count += 1
            print(f"⚠️ [线程{thread_id}] {game_version}版-{level_name}: 尝试重试 {retry_count}/{max_retries} (不缩减token)...")
            time.sleep(TASK_RETRY_DELAY)
            
            # 直接使用原始消息重试，不缩减token
            ai_response = call_gemini_api(messages, max_retries=1)  # 每次重试只尝试1次API调用
            
            if ai_response and not (ai_response.startswith("TOKEN_LIMIT_EXCEEDED")):
                final_analyzed_count = original_count
                original_data_count = original_count
                print(f"✅ [线程{thread_id}] {game_version}版-{level_name}: 重试成功 (第{retry_count}次)")
                break
        
        if ai_response and not (ai_response.startswith("TOKEN_LIMIT_EXCEEDED")):
            print(f"✅ [线程{thread_id}] {game_version}版-{level_name}: AI分析完成")
            # 调试：打印AI响应的前200个字符
            print(f"📝 [线程{thread_id}] {game_version}版-{level_name}: AI响应预览: {ai_response[:200]}...")
            # 统计独立用户数
            unique_users = sample_result['角色名'].nunique()
            print(f"👥 [线程{thread_id}] {game_version}版-{level_name}: 独立用户数: {unique_users}")
            
            # 返回分析结果和统计信息
            return f"{game_version}版-{level_name}", {
                "ai_response": ai_response,
                "total_comments": len(result),
                "analyzed_comments": final_analyzed_count,
                "unique_users": unique_users
            }
        else:
            error_msg = f"AI分析失败 (重试{max_retries}次后仍无响应)"
            print(f"❌ [线程{thread_id}] {game_version}版-{level_name}: {error_msg}")
            return f"{game_version}版-{level_name}", {"error": error_msg}
            
    except Exception as e:
        error_msg = f"分析过程异常: {str(e)}"
        print(f"❌ [线程{thread_id}] {game_version}版-{level_name}: {error_msg}")
        import traceback
        traceback.print_exc()
        return f"{game_version}版-{level_name}", {"error": error_msg}

def main():
    """主函数 - 多线程AI分析不同版本和付费层级的言论数据"""
    print("=== AI言论分析系统 ===")
    
    # 获取昨日日期
    yesterday = get_yesterday_date()
    print(f"分析日期: {yesterday}")
    print(f"使用模型: {GEMINI_MODEL}")
    
    # 定义不同付费层级的查询条件
    pay_levels_e = [
        ("小R用户", "AND uc.tag_value = '小R'"),
        ("中R用户", "AND uc.tag_value = '中R'"),
        ("大R用户", "AND uc.tag_value = '大R'"),
        ("超R用户", "AND uc.tag_value = '超R'"),
        ("零氪用户", "AND uc.tag_value IS NULL")
    ]
    
    pay_levels_d = [
        ("小R用户", "AND uc.tag_value = '小R'"),
        ("中R用户", "AND uc.tag_value = '中R'"),
        ("大R用户", "AND uc.tag_value = '大R'"),
        ("超R用户", "AND uc.tag_value = '超R'"),
        ("零氪用户", "AND uc.tag_value = '非R'")
    ]
    
    # 定义游戏版本
    game_versions = ['D', 'E']
    
    # 使用串行处理，确保每个层级完成后再进入下一个
    results = {}
    start_time = time.time()
    
    # 串行分析，每分钟提交一次
    task_count = 0
    total_tasks = len(game_versions) * 5  # 每个版本5个付费层级
    
    for game_version in game_versions:
        # 根据游戏版本选择对应的付费层级配置
        pay_levels = pay_levels_e if game_version == 'E' else pay_levels_d
        
        for level_name, condition in pay_levels:
            task_count += 1
            print(f"\n🔄 开始分析任务 {task_count}/{total_tasks}: {game_version}版-{level_name}")
            
            # 如果不是第一个任务，等待短时间再开始下一个
            if task_count > 1:
                print(f"⏱️  等待 {LEVEL_ANALYSIS_DELAY} 秒后开始下一个任务...")
                time.sleep(LEVEL_ANALYSIS_DELAY)
            
            # 执行分析
            level_key, analysis_result = analyze_comments_for_level(level_name, condition, yesterday, game_version)
            results[level_key] = analysis_result
            
            # 显示当前进度
            if analysis_result and "ai_response" in analysis_result:
                print(f"✅ 任务 {task_count}/{total_tasks} 完成: {level_key}")
            else:
                error_msg = analysis_result.get("error", "分析失败") if analysis_result else "分析失败"
                print(f"❌ 任务 {task_count}/{total_tasks} 失败: {level_key} - {error_msg}")
            
            print(f"📊 总体进度: {task_count}/{total_tasks} ({task_count/total_tasks*100:.1f}%)")
    
    print(f"\n🎯 所有层级分析完成，共处理 {total_tasks} 个任务")
    
    # 检查失败的任务并重试
    failed_tasks = []
    for level_key, result_data in results.items():
        if result_data and "error" in result_data:
            failed_tasks.append(level_key)
    
    # 对失败的任务进行串行重试（避免并发压力）
    if failed_tasks:
        print(f"\n🔄 检测到 {len(failed_tasks)} 个失败任务，开始串行重试...")
        for level_key in failed_tasks:
            print(f"🔄 重试任务: {level_key}")
            time.sleep(FAILED_TASK_RETRY_DELAY)  # 使用配置的重试间隔
            
            # 解析level_key获取参数
            parts = level_key.split('版-')
            game_version = parts[0]
            level_name = parts[1]
            
            # 获取对应的查询条件
            pay_levels = pay_levels_e if game_version == 'E' else pay_levels_d
            condition = None
            for name, cond in pay_levels:
                if name == level_name:
                    condition = cond
                    break
            
            if condition:
                retry_key, retry_result = analyze_comments_for_level(level_name, condition, yesterday, game_version)
                results[retry_key] = retry_result
                if retry_result and "error" not in retry_result:
                    print(f"✅ 重试成功: {retry_key}")
                else:
                    print(f"❌ 重试仍失败: {retry_key}")
    
    # 确保data目录存在
    os.makedirs('data', exist_ok=True)
    
    # 保存结果
    output_file = f"data/ai_analysis_{yesterday}.json"
    final_results = {}
    
    for level_key in results:
        result_data = results[level_key]
        if result_data and "ai_response" in result_data:
            # 直接保存AI返回的原始响应，不做JSON解析
            final_results[level_key] = {
                "ai_response": result_data["ai_response"],
                "metadata": {
                    "total_comments": result_data.get("total_comments", 0),
                    "analyzed_comments": result_data.get("analyzed_comments", 0),
                    "unique_users": result_data.get("unique_users", 0)
                }
            }
            print(f"✅ {level_key}: 结果已保存")
        else:
            final_results[level_key] = result_data or {"error": "分析失败"}
    
    # 写入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(final_results, f, ensure_ascii=False, indent=2)
    
    end_time = time.time()
    print(f"\n{'='*60}")
    print(f"✅ 所有分析完成!")
    print(f"📁 结果已保存至: {output_file}")
    print(f"⏱️ 总耗时: {end_time - start_time:.1f} 秒")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 