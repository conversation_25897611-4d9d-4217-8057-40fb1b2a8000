# -*- coding: utf-8 -*-
"""
故障恢复和备用机制系统
提供API端点备份、降级策略、故障恢复和服务健康检查功能
"""

import time
import json
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import requests


class ServiceStatus(Enum):
    """服务状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """健康检查结果"""
    service_name: str
    status: ServiceStatus
    response_time: float
    error_message: Optional[str] = None
    checked_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BackupEndpoint:
    """备用端点配置"""
    name: str
    base_url: str
    api_key: str
    priority: int = 5  # 1-10, 1最高优先级
    max_requests_per_minute: int = 60
    timeout: int = 120
    enabled: bool = True
    health_check_url: Optional[str] = None
    
    def __post_init__(self):
        if not self.health_check_url:
            self.health_check_url = f"{self.base_url}/health"


class HealthChecker:
    """服务健康检查器"""
    
    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval
        self.health_status = {}
        self.check_history = {}
        self.running = False
        self.thread = None
        self.lock = threading.Lock()
    
    def start_monitoring(self):
        """开始健康监控"""
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.thread.start()
        print("🏥 健康监控已启动")
    
    def stop_monitoring(self):
        """停止健康监控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        print("🏥 健康监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.running:
            try:
                self._perform_health_checks()
                time.sleep(self.check_interval)
            except Exception as e:
                print(f"❌ 健康检查异常: {e}")
                time.sleep(10)  # 异常时短暂等待
    
    def _perform_health_checks(self):
        """执行健康检查"""
        # 这里可以添加具体的健康检查逻辑
        # 由于AI API通常没有专门的健康检查端点，我们使用简单的连接测试
        pass
    
    def check_endpoint_health(self, endpoint: BackupEndpoint) -> HealthCheckResult:
        """检查单个端点健康状态"""
        start_time = time.time()
        
        try:
            # 尝试简单的连接测试
            response = requests.get(
                endpoint.health_check_url or endpoint.base_url,
                timeout=10,
                headers={'User-Agent': 'HealthChecker/1.0'}
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                status = ServiceStatus.HEALTHY
                error_message = None
            elif response.status_code < 500:
                status = ServiceStatus.DEGRADED
                error_message = f"HTTP {response.status_code}"
            else:
                status = ServiceStatus.UNHEALTHY
                error_message = f"HTTP {response.status_code}"
                
        except requests.exceptions.Timeout:
            response_time = time.time() - start_time
            status = ServiceStatus.UNHEALTHY
            error_message = "连接超时"
            
        except requests.exceptions.ConnectionError:
            response_time = time.time() - start_time
            status = ServiceStatus.UNHEALTHY
            error_message = "连接失败"
            
        except Exception as e:
            response_time = time.time() - start_time
            status = ServiceStatus.UNKNOWN
            error_message = str(e)
        
        result = HealthCheckResult(
            service_name=endpoint.name,
            status=status,
            response_time=response_time,
            error_message=error_message,
            metadata={'endpoint_url': endpoint.base_url}
        )
        
        # 记录检查历史
        with self.lock:
            if endpoint.name not in self.check_history:
                self.check_history[endpoint.name] = []
            
            self.check_history[endpoint.name].append(result)
            
            # 保持历史记录在合理范围内
            if len(self.check_history[endpoint.name]) > 100:
                self.check_history[endpoint.name] = self.check_history[endpoint.name][-50:]
            
            self.health_status[endpoint.name] = result
        
        return result
    
    def get_service_status(self, service_name: str) -> Optional[HealthCheckResult]:
        """获取服务状态"""
        with self.lock:
            return self.health_status.get(service_name)
    
    def get_healthy_services(self) -> List[str]:
        """获取健康的服务列表"""
        healthy_services = []
        with self.lock:
            for service_name, status in self.health_status.items():
                if status.status == ServiceStatus.HEALTHY:
                    healthy_services.append(service_name)
        return healthy_services


class FallbackStrategy:
    """降级策略管理器"""
    
    def __init__(self):
        self.strategies = {}
        self.active_fallbacks = {}
    
    def register_strategy(self, name: str, strategy_func: Callable, 
                         trigger_conditions: Dict[str, Any]):
        """注册降级策略"""
        self.strategies[name] = {
            'function': strategy_func,
            'conditions': trigger_conditions,
            'activated_count': 0,
            'last_activated': None
        }
        print(f"📋 已注册降级策略: {name}")
    
    def should_activate_fallback(self, error_type: str, failure_count: int, 
                               response_time: float = 0) -> Optional[str]:
        """判断是否应该激活降级策略"""
        for strategy_name, strategy_info in self.strategies.items():
            conditions = strategy_info['conditions']
            
            # 检查错误类型条件
            if 'error_types' in conditions:
                if error_type not in conditions['error_types']:
                    continue
            
            # 检查失败次数条件
            if 'min_failure_count' in conditions:
                if failure_count < conditions['min_failure_count']:
                    continue
            
            # 检查响应时间条件
            if 'max_response_time' in conditions:
                if response_time > 0 and response_time > conditions['max_response_time']:
                    return strategy_name
            
            # 检查其他条件
            if failure_count >= conditions.get('min_failure_count', 3):
                return strategy_name
        
        return None
    
    def activate_fallback(self, strategy_name: str, context: Dict[str, Any]) -> Any:
        """激活降级策略"""
        if strategy_name not in self.strategies:
            print(f"❌ 未找到降级策略: {strategy_name}")
            return None
        
        strategy_info = self.strategies[strategy_name]
        strategy_info['activated_count'] += 1
        strategy_info['last_activated'] = datetime.now()
        
        self.active_fallbacks[strategy_name] = {
            'activated_at': datetime.now(),
            'context': context
        }
        
        print(f"🔄 激活降级策略: {strategy_name}")
        
        try:
            return strategy_info['function'](context)
        except Exception as e:
            print(f"❌ 降级策略执行失败: {strategy_name} - {e}")
            return None
    
    def deactivate_fallback(self, strategy_name: str):
        """停用降级策略"""
        if strategy_name in self.active_fallbacks:
            del self.active_fallbacks[strategy_name]
            print(f"✅ 停用降级策略: {strategy_name}")


class BackupManager:
    """备用端点管理器"""
    
    def __init__(self):
        self.backup_endpoints = {}
        self.health_checker = HealthChecker()
        self.fallback_strategy = FallbackStrategy()
        self.usage_stats = {}
        
        # 注册默认降级策略
        self._register_default_strategies()
    
    def add_backup_endpoint(self, endpoint: BackupEndpoint):
        """添加备用端点"""
        self.backup_endpoints[endpoint.name] = endpoint
        print(f"🔗 已添加备用端点: {endpoint.name}")
    
    def remove_backup_endpoint(self, name: str):
        """移除备用端点"""
        if name in self.backup_endpoints:
            del self.backup_endpoints[name]
            print(f"🗑️ 已移除备用端点: {name}")
    
    def get_available_endpoints(self, exclude: List[str] = None) -> List[BackupEndpoint]:
        """获取可用的备用端点"""
        exclude = exclude or []
        available = []
        
        for name, endpoint in self.backup_endpoints.items():
            if not endpoint.enabled or name in exclude:
                continue
            
            # 检查健康状态
            health_status = self.health_checker.get_service_status(name)
            if health_status and health_status.status == ServiceStatus.UNHEALTHY:
                continue
            
            available.append(endpoint)
        
        # 按优先级排序
        available.sort(key=lambda x: x.priority)
        return available
    
    def select_backup_endpoint(self, failed_endpoints: List[str] = None) -> Optional[BackupEndpoint]:
        """选择最佳备用端点"""
        available = self.get_available_endpoints(exclude=failed_endpoints)
        
        if not available:
            return None
        
        # 选择优先级最高且使用次数最少的端点
        best_endpoint = None
        min_usage = float('inf')
        
        for endpoint in available:
            usage_count = self.usage_stats.get(endpoint.name, 0)
            if usage_count < min_usage:
                min_usage = usage_count
                best_endpoint = endpoint
        
        return best_endpoint
    
    def record_endpoint_usage(self, endpoint_name: str, success: bool):
        """记录端点使用情况"""
        if endpoint_name not in self.usage_stats:
            self.usage_stats[endpoint_name] = 0
        
        self.usage_stats[endpoint_name] += 1
        
        # 如果失败，可以考虑降低该端点的优先级
        if not success and endpoint_name in self.backup_endpoints:
            endpoint = self.backup_endpoints[endpoint_name]
            if endpoint.priority < 10:
                endpoint.priority += 1  # 降低优先级
    
    def _register_default_strategies(self):
        """注册默认降级策略"""
        
        def simple_retry_strategy(context):
            """简单重试策略"""
            print("🔄 执行简单重试策略")
            return None
        
        def cache_response_strategy(context):
            """缓存响应策略"""
            print("💾 尝试使用缓存响应")
            # 这里可以实现缓存逻辑
            return None
        
        def minimal_response_strategy(context):
            """最小响应策略"""
            print("📝 返回最小化响应")
            return "由于服务暂时不可用，返回简化响应。请稍后重试。"
        
        # 注册策略
        self.fallback_strategy.register_strategy(
            "simple_retry",
            simple_retry_strategy,
            {"error_types": ["network_error", "timeout_error"], "min_failure_count": 2}
        )
        
        self.fallback_strategy.register_strategy(
            "cache_response",
            cache_response_strategy,
            {"error_types": ["rate_limit", "server_error"], "min_failure_count": 3}
        )
        
        self.fallback_strategy.register_strategy(
            "minimal_response",
            minimal_response_strategy,
            {"min_failure_count": 5}
        )
    
    def start_health_monitoring(self):
        """启动健康监控"""
        self.health_checker.start_monitoring()
    
    def stop_health_monitoring(self):
        """停止健康监控"""
        self.health_checker.stop_monitoring()
    
    def get_backup_stats(self) -> Dict:
        """获取备用系统统计信息"""
        return {
            'total_endpoints': len(self.backup_endpoints),
            'enabled_endpoints': sum(1 for ep in self.backup_endpoints.values() if ep.enabled),
            'healthy_endpoints': len(self.health_checker.get_healthy_services()),
            'usage_stats': self.usage_stats,
            'active_fallbacks': list(self.fallback_strategy.active_fallbacks.keys()),
            'strategy_stats': {
                name: {
                    'activated_count': info['activated_count'],
                    'last_activated': info['last_activated'].isoformat() if info['last_activated'] else None
                }
                for name, info in self.fallback_strategy.strategies.items()
            }
        }
