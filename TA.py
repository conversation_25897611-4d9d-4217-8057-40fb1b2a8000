# -*- coding: utf-8 -*-
"""精简TA类 - 只保留SQL查询核心功能"""

import requests
import json
import pandas as pd
import time
from SET import Setting

class Ta:
    def __init__(self, game):
        self.st = Setting(game)
        # 连接重试配置
        self.connection_retry_max = 3
        self.connection_retry_delay = 2

    def sql_query(self, sql):
        """SQL查询接口 - 增加连接重试机制"""
        retry_count = 0
        
        while retry_count < self.connection_retry_max:
            try:
                head = {"Content-Type": "application/x-www-form-urlencoded"}
                pa = {"token": self.st.token, "sql": sql, "format": "csv_header"}
                response = requests.post(self.st.sql_url, headers=head, data=pa, timeout=30)
                
                # 检查响应状态
                if response.status_code != 200:
                    error_msg = f"HTTP错误: {response.status_code}"
                    print(f"{error_msg} (尝试 {retry_count + 1}/{self.connection_retry_max})")
                    print(f"响应内容: {response.text[:500]}...")
                    
                    if retry_count < self.connection_retry_max - 1:
                        print(f"等待 {self.connection_retry_delay} 秒后重试...")
                        time.sleep(self.connection_retry_delay)
                        retry_count += 1
                        continue
                    else:
                        print(f"达到最大重试次数，返回None")
                        return None
                
                # 检查响应内容是否为空
                if not response.text.strip():
                    print(f"响应内容为空 (尝试 {retry_count + 1}/{self.connection_retry_max})")
                    
                    if retry_count < self.connection_retry_max - 1:
                        print(f"等待 {self.connection_retry_delay} 秒后重试...")
                        time.sleep(self.connection_retry_delay)
                        retry_count += 1
                        continue
                    else:
                        print(f"达到最大重试次数，返回None")
                        return None
                
                # 处理CSV格式响应
                # 设置响应编码为UTF-8
                response.encoding = 'utf-8'
                
                # 使用pandas直接读取CSV
                from io import StringIO
                csv_data = StringIO(response.text)
                df = pd.read_csv(csv_data)
                
                # 去除重复列
                df = df.loc[:, ~df.columns.duplicated()]
                
                return df
                
            except (requests.exceptions.RequestException, requests.exceptions.Timeout, 
                    requests.exceptions.ConnectionError, Exception) as e:
                retry_count += 1
                error_msg = f"连接错误: {str(e)}"
                print(f"{error_msg} (尝试 {retry_count}/{self.connection_retry_max})")
                
                if retry_count < self.connection_retry_max:
                    print(f"等待 {self.connection_retry_delay} 秒后重试...")
                    time.sleep(self.connection_retry_delay)
                else:
                    print(f"SQL查询最终失败: {sql}")
                    print(f"最终错误: {e}")
                    return None
        
        return None
    
    def query_recharge(self, date):
        """查询充值数据"""
        sql = self.sql_recharge_template.format(date)
        return self.sql_query(sql)
    
    def query_create_role(self, date):
        """查询创角数据"""
        sql = self.sql_create_role_template.format(date)
        return self.sql_query(sql) 