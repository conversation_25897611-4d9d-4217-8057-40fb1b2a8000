# -*- coding: utf-8 -*-
"""
Token 优化和请求负载管理系统
提供智能 Token 管理、请求批处理、负载均衡和资源优化功能
"""

import json
import time
import math
import threading
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from queue import Queue, PriorityQueue
from datetime import datetime, timedelta
import hashlib

# 尝试导入 tiktoken 进行精确 token 计算
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True
    TIKTOKEN_ENCODING = tiktoken.get_encoding("cl100k_base")
    print("✅ 已启用tiktoken精确token计算")
except ImportError:
    TIKTOKEN_AVAILABLE = False
    TIKTOKEN_ENCODING = None
    print("⚠️ tiktoken未安装，使用近似token计算")


@dataclass
class TokenUsage:
    """Token使用统计"""
    input_tokens: int = 0
    output_tokens: int = 0
    total_tokens: int = 0
    estimated_cost: float = 0.0
    
    def __post_init__(self):
        if self.total_tokens == 0:
            self.total_tokens = self.input_tokens + self.output_tokens


@dataclass
class RequestMetadata:
    """请求元数据"""
    request_id: str
    step: str
    priority: int = 5  # 1-10, 1最高优先级
    estimated_tokens: int = 0
    max_tokens: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    timeout: int = 600  # 默认10分钟超时
    retry_count: int = 0
    
    def __lt__(self, other):
        # 优先级队列排序：优先级高的先执行，同优先级按创建时间
        if self.priority != other.priority:
            return self.priority < other.priority
        return self.created_at < other.created_at


class TokenEstimator:
    """Token估算器"""
    
    # 不同模型的token成本（每1000 tokens的价格，美元）
    MODEL_COSTS = {
        'gemini-2.5-flash-preview-05-20': {'input': 0.0005, 'output': 0.002},
        'gemini-2.5-pro-preview-06-05': {'input': 0.003, 'output': 0.012},
        'gpt-4': {'input': 0.03, 'output': 0.06},
        'gpt-3.5-turbo': {'input': 0.001, 'output': 0.002}
    }
    
    def __init__(self):
        self.cache = {}  # 缓存计算结果
    
    def estimate_tokens(self, text: str, use_cache: bool = True) -> int:
        """估算文本的token数量"""
        if use_cache:
            text_hash = hashlib.md5(text.encode()).hexdigest()
            if text_hash in self.cache:
                return self.cache[text_hash]
        
        if TIKTOKEN_AVAILABLE and TIKTOKEN_ENCODING:
            try:
                tokens = len(TIKTOKEN_ENCODING.encode(text))
                if use_cache:
                    self.cache[text_hash] = tokens
                return tokens
            except Exception as e:
                print(f"⚠️ tiktoken计算失败: {e}")
        
        # Fallback到近似估算
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        # 中文字符：每个字符约1个token
        # 英文字符：每3-4个字符约1个token
        estimated_tokens = chinese_chars + (other_chars // 3)
        
        if use_cache:
            self.cache[text_hash] = estimated_tokens
        
        return max(1, estimated_tokens)
    
    def estimate_messages_tokens(self, messages: List[Dict]) -> int:
        """估算消息列表的总token数"""
        total_tokens = 0
        
        for message in messages:
            # 消息结构的开销（role, content等字段）
            total_tokens += 4
            
            # 内容token数
            content = message.get('content', '')
            if content:
                total_tokens += self.estimate_tokens(content)
        
        # 对话结构的额外开销
        total_tokens += 2
        
        return total_tokens
    
    def calculate_cost(self, token_usage: TokenUsage, model: str) -> float:
        """计算API调用成本"""
        if model not in self.MODEL_COSTS:
            # 使用默认成本
            model = 'gpt-3.5-turbo'
        
        costs = self.MODEL_COSTS[model]
        input_cost = (token_usage.input_tokens / 1000) * costs['input']
        output_cost = (token_usage.output_tokens / 1000) * costs['output']
        
        return input_cost + output_cost
    
    def optimize_messages(self, messages: List[Dict], max_tokens: int, 
                         preserve_system: bool = True) -> Tuple[List[Dict], int]:
        """优化消息列表以适应token限制"""
        if not messages:
            return messages, 0
        
        current_tokens = self.estimate_messages_tokens(messages)
        if current_tokens <= max_tokens:
            return messages, current_tokens
        
        optimized_messages = []
        
        # 保留系统消息
        if preserve_system and messages[0].get('role') == 'system':
            optimized_messages.append(messages[0])
            remaining_messages = messages[1:]
        else:
            remaining_messages = messages
        
        # 计算可用token数
        used_tokens = self.estimate_messages_tokens(optimized_messages)
        available_tokens = max_tokens - used_tokens - 100  # 保留100token缓冲
        
        # 从最新消息开始，逐步添加直到达到限制
        for message in reversed(remaining_messages):
            message_tokens = self.estimate_tokens(message.get('content', '')) + 4
            
            if used_tokens + message_tokens <= available_tokens:
                optimized_messages.insert(-len(optimized_messages) if optimized_messages else 0, message)
                used_tokens += message_tokens
            else:
                # 尝试截断当前消息
                content = message.get('content', '')
                if content and len(content) > 100:
                    # 计算可以保留的内容长度
                    available_for_content = available_tokens - used_tokens - 4
                    if available_for_content > 50:
                        # 按token比例截断
                        content_tokens = self.estimate_tokens(content)
                        keep_ratio = available_for_content / content_tokens
                        keep_length = int(len(content) * keep_ratio * 0.9)  # 保守估计
                        
                        truncated_content = content[:keep_length] + "...[内容已截断]"
                        truncated_message = message.copy()
                        truncated_message['content'] = truncated_content
                        
                        optimized_messages.insert(-len(optimized_messages) if optimized_messages else 0, 
                                                truncated_message)
                        used_tokens += self.estimate_tokens(truncated_content) + 4
                break
        
        final_tokens = self.estimate_messages_tokens(optimized_messages)
        return optimized_messages, final_tokens


class RequestQueue:
    """智能请求队列管理器"""
    
    def __init__(self, max_concurrent: int = 3, rate_limit_per_minute: int = 60):
        self.max_concurrent = max_concurrent
        self.rate_limit_per_minute = rate_limit_per_minute
        self.queue = PriorityQueue()
        self.active_requests = {}
        self.request_history = []
        self.lock = threading.Lock()
        
    def add_request(self, metadata: RequestMetadata, messages: List[Dict], 
                   api_params: Dict) -> str:
        """添加请求到队列"""
        request_data = {
            'metadata': metadata,
            'messages': messages,
            'api_params': api_params,
            'queued_at': datetime.now()
        }
        
        with self.lock:
            self.queue.put((metadata, request_data))
        
        print(f"📥 请求已加入队列: {metadata.request_id} (优先级: {metadata.priority})")
        return metadata.request_id
    
    def can_process_request(self) -> bool:
        """检查是否可以处理新请求"""
        with self.lock:
            # 检查并发限制
            if len(self.active_requests) >= self.max_concurrent:
                return False
            
            # 检查速率限制
            now = datetime.now()
            recent_requests = [
                req for req in self.request_history 
                if now - req['completed_at'] < timedelta(minutes=1)
            ]
            
            if len(recent_requests) >= self.rate_limit_per_minute:
                return False
            
            return True
    
    def get_next_request(self) -> Optional[Tuple[RequestMetadata, Dict]]:
        """获取下一个待处理请求"""
        if not self.can_process_request():
            return None
        
        try:
            metadata, request_data = self.queue.get_nowait()
            
            with self.lock:
                self.active_requests[metadata.request_id] = {
                    'metadata': metadata,
                    'started_at': datetime.now()
                }
            
            return metadata, request_data
        except:
            return None
    
    def complete_request(self, request_id: str, success: bool = True, 
                        token_usage: Optional[TokenUsage] = None):
        """标记请求完成"""
        with self.lock:
            if request_id in self.active_requests:
                request_info = self.active_requests.pop(request_id)
                
                self.request_history.append({
                    'request_id': request_id,
                    'metadata': request_info['metadata'],
                    'started_at': request_info['started_at'],
                    'completed_at': datetime.now(),
                    'success': success,
                    'token_usage': token_usage
                })
                
                # 保持历史记录在合理范围内
                if len(self.request_history) > 1000:
                    self.request_history = self.request_history[-500:]
    
    def get_queue_status(self) -> Dict:
        """获取队列状态"""
        with self.lock:
            return {
                'queued_requests': self.queue.qsize(),
                'active_requests': len(self.active_requests),
                'completed_requests': len(self.request_history),
                'can_process_new': self.can_process_request()
            }


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self):
        self.provider_stats = {}
        self.provider_weights = {}
    
    def update_provider_stats(self, provider: str, success: bool, 
                            response_time: float, error_type: str = None):
        """更新提供商统计信息"""
        if provider not in self.provider_stats:
            self.provider_stats[provider] = {
                'total_requests': 0,
                'successful_requests': 0,
                'total_response_time': 0.0,
                'error_counts': {}
            }
        
        stats = self.provider_stats[provider]
        stats['total_requests'] += 1
        
        if success:
            stats['successful_requests'] += 1
            stats['total_response_time'] += response_time
        else:
            if error_type:
                stats['error_counts'][error_type] = stats['error_counts'].get(error_type, 0) + 1
        
        # 更新权重
        self._update_weights()
    
    def _update_weights(self):
        """更新提供商权重"""
        for provider, stats in self.provider_stats.items():
            if stats['total_requests'] == 0:
                weight = 1.0
            else:
                success_rate = stats['successful_requests'] / stats['total_requests']
                avg_response_time = (stats['total_response_time'] / 
                                   max(1, stats['successful_requests']))
                
                # 权重计算：成功率 * (1 / 响应时间权重)
                time_weight = 1.0 / (1.0 + avg_response_time / 10.0)  # 10秒为基准
                weight = success_rate * time_weight
            
            self.provider_weights[provider] = max(0.1, weight)  # 最小权重0.1
    
    def select_provider(self, available_providers: List[str]) -> str:
        """选择最佳提供商"""
        if not available_providers:
            return None
        
        if len(available_providers) == 1:
            return available_providers[0]
        
        # 基于权重选择
        total_weight = sum(self.provider_weights.get(p, 1.0) for p in available_providers)
        
        if total_weight == 0:
            return available_providers[0]
        
        # 加权随机选择
        import random
        rand_val = random.uniform(0, total_weight)
        current_weight = 0
        
        for provider in available_providers:
            current_weight += self.provider_weights.get(provider, 1.0)
            if rand_val <= current_weight:
                return provider
        
        return available_providers[0]
    
    def get_provider_stats(self) -> Dict:
        """获取提供商统计信息"""
        result = {}
        for provider, stats in self.provider_stats.items():
            if stats['total_requests'] > 0:
                success_rate = stats['successful_requests'] / stats['total_requests']
                avg_response_time = (stats['total_response_time'] / 
                                   max(1, stats['successful_requests']))
            else:
                success_rate = 0.0
                avg_response_time = 0.0
            
            result[provider] = {
                'total_requests': stats['total_requests'],
                'success_rate': success_rate,
                'average_response_time': avg_response_time,
                'weight': self.provider_weights.get(provider, 1.0),
                'error_counts': stats['error_counts']
            }
        
        return result
