# -*- coding: utf-8 -*-
"""
AI API 监控和验证系统
提供响应验证、性能监控、错误分类和智能重试功能
"""

import json
import time
import re
import random
import logging
from typing import Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta


@dataclass
class APIMetrics:
    """API调用指标"""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    empty_responses: int = 0
    total_response_time: float = 0.0
    total_tokens_used: int = 0
    error_counts: Dict[str, int] = field(default_factory=dict)
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_calls / max(1, self.total_calls)
    
    @property
    def failure_rate(self) -> float:
        """失败率"""
        return self.failed_calls / max(1, self.total_calls)
    
    @property
    def empty_response_rate(self) -> float:
        """空响应率"""
        return self.empty_responses / max(1, self.total_calls)
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / max(1, self.successful_calls)


class ErrorType(Enum):
    """错误类型枚举"""
    RATE_LIMIT = "rate_limit"
    SERVER_ERROR = "server_error"
    NETWORK_ERROR = "network_error"
    TOKEN_LIMIT = "token_limit"
    EMPTY_RESPONSE = "empty_response"
    AUTH_ERROR = "auth_error"
    CONTENT_FILTER = "content_filter"
    TIMEOUT_ERROR = "timeout_error"
    UNKNOWN_ERROR = "unknown_error"


class ErrorClassifier:
    """智能错误分类器"""
    
    ERROR_PATTERNS = {
        ErrorType.RATE_LIMIT: [
            r'429', r'rate.?limit', r'too.?many.?requests',
            r'quota.?exceeded', r'requests.?per.?minute', r'requests.?per.?day'
        ],
        ErrorType.SERVER_ERROR: [
            r'5\d{2}', r'server.?error', r'internal.?error',
            r'service.?unavailable', r'bad.?gateway', r'gateway.?timeout'
        ],
        ErrorType.NETWORK_ERROR: [
            r'connection', r'timeout', r'network', r'dns',
            r'unreachable', r'connection.?reset', r'connection.?refused'
        ],
        ErrorType.TOKEN_LIMIT: [
            r'token.?count', r'token.?limit', r'context.?length',
            r'maximum.?tokens', r'exceeds.?the.?maximum', r'input.?token.?count'
        ],
        ErrorType.AUTH_ERROR: [
            r'401', r'unauthorized', r'invalid.?key',
            r'authentication', r'forbidden', r'api.?key'
        ],
        ErrorType.CONTENT_FILTER: [
            r'content.?filter', r'content.?policy', r'safety.?filter',
            r'inappropriate.?content', r'blocked.?content'
        ],
        ErrorType.TIMEOUT_ERROR: [
            r'timeout', r'timed.?out', r'request.?timeout',
            r'read.?timeout', r'connection.?timeout'
        ]
    }
    
    @classmethod
    def classify_error(cls, error_str: str, response_content: str = None) -> ErrorType:
        """分类错误类型"""
        if not response_content or response_content.strip() == "":
            return ErrorType.EMPTY_RESPONSE
        
        error_lower = error_str.lower()
        
        for error_type, patterns in cls.ERROR_PATTERNS.items():
            if any(re.search(pattern, error_lower) for pattern in patterns):
                return error_type
        
        return ErrorType.UNKNOWN_ERROR


class ResponseValidator:
    """响应验证器"""
    
    def __init__(self):
        self.validation_history = []
    
    def validate_response(self, response: str, expected_format: str = None, 
                         context: str = None) -> dict:
        """验证响应内容"""
        validation_result = {
            'is_valid': True,
            'issues': [],
            'confidence': 1.0,
            'response_length': len(response) if response else 0,
            'validation_time': datetime.now().isoformat(),
            'context': context
        }
        
        # 基础检查
        if not response or response.strip() == "":
            validation_result['is_valid'] = False
            validation_result['issues'].append('Empty response')
            validation_result['confidence'] = 0.0
            return validation_result
        
        # 长度检查
        if len(response) < 10:
            validation_result['issues'].append('Response too short')
            validation_result['confidence'] *= 0.5
        elif len(response) > 100000:
            validation_result['issues'].append('Response unusually long')
            validation_result['confidence'] *= 0.9
        
        # 格式检查
        if expected_format == 'json':
            try:
                parsed_json = json.loads(response)
                validation_result['parsed_data'] = parsed_json
            except json.JSONDecodeError as e:
                validation_result['issues'].append(f'Invalid JSON format: {str(e)}')
                validation_result['confidence'] *= 0.3
        
        # 内容质量检查
        if response.count('```') % 2 != 0:
            validation_result['issues'].append('Unmatched code blocks')
            validation_result['confidence'] *= 0.8
        
        # 检查是否包含错误信息
        error_indicators = ['error', 'failed', 'exception', 'traceback', 'stack trace']
        error_count = sum(1 for indicator in error_indicators if indicator in response.lower())
        if error_count > 0:
            validation_result['issues'].append(f'Contains {error_count} error indicators')
            validation_result['confidence'] *= max(0.3, 1.0 - error_count * 0.2)
        
        # 检查重复内容
        lines = response.split('\n')
        if len(lines) > 10:
            unique_lines = set(lines)
            repetition_ratio = 1 - len(unique_lines) / len(lines)
            if repetition_ratio > 0.5:
                validation_result['issues'].append(f'High repetition ratio: {repetition_ratio:.2f}')
                validation_result['confidence'] *= 0.6
        
        # 检查是否截断
        truncation_indicators = ['...', '[truncated]', '[cut off]', 'response truncated']
        if any(indicator in response.lower() for indicator in truncation_indicators):
            validation_result['issues'].append('Response appears truncated')
            validation_result['confidence'] *= 0.7
        
        # 设置最终有效性
        if validation_result['confidence'] < 0.5:
            validation_result['is_valid'] = False
        
        # 记录验证历史
        self.validation_history.append(validation_result)
        
        return validation_result
    
    def get_validation_stats(self) -> dict:
        """获取验证统计信息"""
        if not self.validation_history:
            return {}
        
        total_validations = len(self.validation_history)
        valid_responses = sum(1 for v in self.validation_history if v['is_valid'])
        avg_confidence = sum(v['confidence'] for v in self.validation_history) / total_validations
        
        return {
            'total_validations': total_validations,
            'valid_responses': valid_responses,
            'validation_success_rate': valid_responses / total_validations,
            'average_confidence': avg_confidence,
            'common_issues': self._get_common_issues()
        }
    
    def _get_common_issues(self) -> dict:
        """获取常见问题统计"""
        issue_counts = {}
        for validation in self.validation_history:
            for issue in validation['issues']:
                issue_counts[issue] = issue_counts.get(issue, 0) + 1
        
        return dict(sorted(issue_counts.items(), key=lambda x: x[1], reverse=True))


class CircuitBreaker:
    """熔断器模式实现"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 300, 
                 half_open_max_calls: int = 3):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.half_open_max_calls = half_open_max_calls
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        self.half_open_calls = 0
    
    def can_execute(self) -> bool:
        """检查是否可以执行请求"""
        current_time = time.time()
        
        if self.state == 'CLOSED':
            return True
        elif self.state == 'OPEN':
            if current_time - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                self.half_open_calls = 0
                print(f"🟡 熔断器进入半开状态，开始测试恢复")
                return True
            return False
        else:  # HALF_OPEN
            return self.half_open_calls < self.half_open_max_calls
    
    def record_success(self):
        """记录成功"""
        if self.state == 'HALF_OPEN':
            self.success_count += 1
            self.half_open_calls += 1
            if self.success_count >= self.half_open_max_calls:
                self.state = 'CLOSED'
                self.failure_count = 0
                self.success_count = 0
                print(f"🟢 熔断器恢复正常状态")
        else:
            self.failure_count = max(0, self.failure_count - 1)
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == 'HALF_OPEN':
            self.state = 'OPEN'
            print(f"🔴 熔断器重新开启: 半开状态测试失败")
        elif self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'
            print(f"🔴 熔断器开启: 连续失败 {self.failure_count} 次")
    
    def get_state(self) -> dict:
        """获取熔断器状态"""
        return {
            'state': self.state,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure_time': self.last_failure_time,
            'can_execute': self.can_execute()
        }


class AdaptiveRetryStrategy:
    """自适应重试策略"""
    
    RETRY_CONFIGS = {
        ErrorType.RATE_LIMIT: {
            'max_retries': 5,
            'base_delay': 60,
            'strategy': 'fixed_delay',
            'jitter': True,
            'backoff_multiplier': 1.0
        },
        ErrorType.SERVER_ERROR: {
            'max_retries': 4,
            'base_delay': 5,
            'strategy': 'exponential_backoff',
            'max_delay': 120,
            'backoff_multiplier': 2.0
        },
        ErrorType.NETWORK_ERROR: {
            'max_retries': 6,
            'base_delay': 2,
            'strategy': 'exponential_backoff',
            'max_delay': 30,
            'backoff_multiplier': 1.5
        },
        ErrorType.EMPTY_RESPONSE: {
            'max_retries': 3,
            'base_delay': 10,
            'strategy': 'linear_backoff',
            'validation_required': True,
            'backoff_multiplier': 1.0
        },
        ErrorType.TOKEN_LIMIT: {
            'max_retries': 2,
            'base_delay': 5,
            'strategy': 'immediate',
            'requires_optimization': True
        },
        ErrorType.TIMEOUT_ERROR: {
            'max_retries': 4,
            'base_delay': 15,
            'strategy': 'exponential_backoff',
            'max_delay': 180,
            'backoff_multiplier': 2.0
        }
    }
    
    def get_retry_config(self, error_type: ErrorType) -> dict:
        """获取重试配置"""
        return self.RETRY_CONFIGS.get(error_type, {
            'max_retries': 3,
            'base_delay': 10,
            'strategy': 'exponential_backoff',
            'backoff_multiplier': 2.0
        })
    
    def calculate_delay(self, attempt: int, config: dict) -> float:
        """计算等待时间"""
        strategy = config.get('strategy', 'exponential_backoff')
        base_delay = config.get('base_delay', 10)
        max_delay = config.get('max_delay', 300)
        multiplier = config.get('backoff_multiplier', 2.0)
        
        if strategy == 'fixed_delay':
            delay = base_delay
        elif strategy == 'linear_backoff':
            delay = base_delay * attempt
        elif strategy == 'exponential_backoff':
            delay = base_delay * (multiplier ** (attempt - 1))
        else:  # immediate
            delay = 0
        
        # 应用最大延迟限制
        delay = min(delay, max_delay)
        
        # 添加抖动
        if config.get('jitter', False):
            jitter = random.uniform(-delay * 0.1, delay * 0.1)
            delay = max(0, delay + jitter)
        
        return delay
    
    def should_retry(self, error_type: ErrorType, attempt: int, 
                    response_validation: dict = None) -> bool:
        """判断是否应该重试"""
        config = self.get_retry_config(error_type)
        
        # 检查重试次数限制
        if attempt >= config.get('max_retries', 3):
            return False
        
        # 特殊处理
        if error_type == ErrorType.TOKEN_LIMIT and config.get('requires_optimization'):
            # Token限制需要优化请求而不是简单重试
            return False
        
        if error_type == ErrorType.EMPTY_RESPONSE and config.get('validation_required'):
            # 空响应需要验证是否值得重试
            if response_validation and response_validation.get('confidence', 0) < 0.1:
                return False
        
        return True
