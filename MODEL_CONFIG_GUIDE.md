# AI模型配置系统使用指南

## 概述

该系统支持为不同的分析步骤配置AI模型，使用统一的Gemini Pro模型。

## 系统架构

### 1. 分析步骤

- **step1_analysis**: 数据收集和AI分析（10个用户层级分析）
- **step2_html_generation**: HTML报告生成（汇总数据）

### 2. 模型配置结构

```json
{
  "api_key": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
  "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/",
  "models": {
    "step1_analysis": {
      "model": "gemini-2.5-flash-preview-05-20",
      "reasoning_effort": "high",
      "temperature": 0.7,
      "max_tokens": 65536,
      "top_p": 0.95
    },
    "step2_html_generation": {
      "model": "gemini-2.5-pro-preview-06-05",
      "temperature": 0.7,
      "max_tokens": 65536,
      "top_p": 0.95
    }
  }
}
```

## 使用方法

### 1. 查看当前配置

```bash
python -c "from model_config import get_model_config; get_model_config().list_configs()"
```

### 2. 程序逻辑

**Step1**: 数据分析
- 拉取10个用户层级数据（D版5个 + E版5个）
- 每个层级单独调用AI分析
- **AI调用次数**: 10次
- 模型: `gemini-2.5-flash-preview-05-20` (高推理模式)

**Step2**: HTML生成
- 汇总Step1的10个分析结果
- 生成完整HTML报告
- **AI调用次数**: 1次
- 模型: `gemini-2.5-pro-preview-06-05`

**Step3**: HTML转图片
- 使用Playwright将HTML转换为PNG图片
- **不涉及AI调用**

## 配置参数说明

- `model`: 使用的AI模型名称
- `reasoning_effort`: 推理强度（仅Flash模型支持："low"/"medium"/"high"）
- `temperature`: 生成随机性（0.0-1.0）
- `max_tokens`: 最大输出长度
- `top_p`: 核采样参数

## 修改配置

直接编辑 `model_config.json` 文件，或使用代码：

```python
from model_config import get_model_config

config = get_model_config()
config.update_config('step1_analysis', {
    "model": "gemini-2.5-flash-preview-05-20",
    "reasoning_effort": "high",
    "temperature": 0.7,
    "max_tokens": 65536,
    "top_p": 0.95
})
```

## 注意事项

1. **差异化模型**: Step1使用Flash模型进行深度分析，Step2使用Pro模型确保生成稳定性
2. **推理强度**: 仅Flash模型支持reasoning_effort参数，Pro模型会自动忽略此参数
3. **配置简化**: 移除了复杂的多提供商配置，统一使用Gemini
4. **Step3澄清**: HTML转图片功能不需要AI配置

## 文件结构

- `model_config.py`: 模型配置管理类
- `model_config.json`: 配置文件
- `ai_analysis.py`: Step1数据分析
- `html_generator.py`: Step2 HTML生成
- `html_to_image.py`: Step3图片转换（非AI） 