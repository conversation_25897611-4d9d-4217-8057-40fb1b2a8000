# -*- coding: utf-8 -*-
"""
全面的日志记录和调试系统
提供结构化日志记录、性能分析、问题诊断和调试工具
"""

import json
import time
import logging
import traceback
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from pathlib import Path
import os
import sys
from contextlib import contextmanager


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    component: str
    message: str
    context: Dict[str, Any] = field(default_factory=dict)
    duration: Optional[float] = None
    error_details: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PerformanceMetric:
    """性能指标"""
    operation: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def finish(self, success: bool = True, **metadata):
        """完成性能测量"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.metadata.update(metadata)


class StructuredLogger:
    """结构化日志记录器"""
    
    def __init__(self, name: str = "ai_api", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # 设置日志文件
        self.log_file = self.log_dir / f"{name}_{datetime.now().strftime('%Y%m%d')}.log"
        self.error_log_file = self.log_dir / f"{name}_errors_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 配置标准日志记录器
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 错误文件处理器
        error_handler = logging.FileHandler(self.error_log_file, encoding='utf-8')
        error_handler.setLevel(logging.ERROR)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        file_handler.setFormatter(formatter)
        error_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
        
        # 结构化日志存储
        self.structured_logs = []
        self.max_logs = 10000
        self.lock = threading.Lock()
    
    def log(self, level: str, component: str, message: str, 
            context: Dict[str, Any] = None, duration: float = None,
            error_details: Dict[str, Any] = None):
        """记录结构化日志"""
        
        entry = LogEntry(
            timestamp=datetime.now().isoformat(),
            level=level.upper(),
            component=component,
            message=message,
            context=context or {},
            duration=duration,
            error_details=error_details
        )
        
        with self.lock:
            self.structured_logs.append(entry)
            
            # 保持日志数量在限制内
            if len(self.structured_logs) > self.max_logs:
                self.structured_logs = self.structured_logs[-self.max_logs//2:]
        
        # 同时记录到标准日志
        log_message = f"[{component}] {message}"
        if context:
            log_message += f" | Context: {json.dumps(context, ensure_ascii=False)}"
        
        if level.upper() == 'DEBUG':
            self.logger.debug(log_message)
        elif level.upper() == 'INFO':
            self.logger.info(log_message)
        elif level.upper() == 'WARNING':
            self.logger.warning(log_message)
        elif level.upper() == 'ERROR':
            self.logger.error(log_message)
            if error_details:
                self.logger.error(f"Error details: {json.dumps(error_details, ensure_ascii=False)}")
        elif level.upper() == 'CRITICAL':
            self.logger.critical(log_message)
    
    def debug(self, component: str, message: str, **kwargs):
        """调试日志"""
        self.log('DEBUG', component, message, **kwargs)
    
    def info(self, component: str, message: str, **kwargs):
        """信息日志"""
        self.log('INFO', component, message, **kwargs)
    
    def warning(self, component: str, message: str, **kwargs):
        """警告日志"""
        self.log('WARNING', component, message, **kwargs)
    
    def error(self, component: str, message: str, exception: Exception = None, **kwargs):
        """错误日志"""
        error_details = None
        if exception:
            error_details = {
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'traceback': traceback.format_exc()
            }
        
        self.log('ERROR', component, message, error_details=error_details, **kwargs)
    
    def critical(self, component: str, message: str, **kwargs):
        """严重错误日志"""
        self.log('CRITICAL', component, message, **kwargs)
    
    def get_logs(self, level: str = None, component: str = None, 
                 since: datetime = None, limit: int = 100) -> List[LogEntry]:
        """获取日志"""
        with self.lock:
            logs = self.structured_logs.copy()
        
        # 过滤条件
        if level:
            logs = [log for log in logs if log.level == level.upper()]
        
        if component:
            logs = [log for log in logs if log.component == component]
        
        if since:
            since_str = since.isoformat()
            logs = [log for log in logs if log.timestamp >= since_str]
        
        # 返回最新的日志
        return logs[-limit:] if limit else logs
    
    def export_logs(self, filename: str = None, format: str = 'json'):
        """导出日志"""
        if not filename:
            filename = f"exported_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{format}"
        
        export_path = self.log_dir / filename
        
        with self.lock:
            logs_data = [log.to_dict() for log in self.structured_logs]
        
        if format == 'json':
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(logs_data, f, ensure_ascii=False, indent=2)
        elif format == 'csv':
            import csv
            if logs_data:
                with open(export_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=logs_data[0].keys())
                    writer.writeheader()
                    writer.writerows(logs_data)
        
        print(f"📄 日志已导出到: {export_path}")
        return export_path


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.metrics = []
        self.active_operations = {}
        self.lock = threading.Lock()
    
    @contextmanager
    def profile(self, operation: str, **metadata):
        """性能分析上下文管理器"""
        metric = PerformanceMetric(
            operation=operation,
            start_time=time.time(),
            metadata=metadata
        )
        
        operation_id = f"{operation}_{id(metric)}"
        
        with self.lock:
            self.active_operations[operation_id] = metric
        
        try:
            self.logger.debug('PROFILER', f"开始操作: {operation}", context=metadata)
            yield metric
            metric.finish(success=True)
            
        except Exception as e:
            metric.finish(success=False, error=str(e))
            self.logger.error('PROFILER', f"操作失败: {operation}", exception=e, context=metadata)
            raise
        
        finally:
            with self.lock:
                self.active_operations.pop(operation_id, None)
                self.metrics.append(metric)
                
                # 保持指标数量在限制内
                if len(self.metrics) > 1000:
                    self.metrics = self.metrics[-500:]
            
            self.logger.info('PROFILER', f"完成操作: {operation}", 
                           context={'duration': metric.duration, 'success': metric.success})
    
    def get_performance_stats(self, operation: str = None, 
                            since: datetime = None) -> Dict[str, Any]:
        """获取性能统计"""
        with self.lock:
            metrics = self.metrics.copy()
        
        if operation:
            metrics = [m for m in metrics if m.operation == operation]
        
        if since:
            since_timestamp = since.timestamp()
            metrics = [m for m in metrics if m.start_time >= since_timestamp]
        
        if not metrics:
            return {}
        
        durations = [m.duration for m in metrics if m.duration is not None]
        successful_metrics = [m for m in metrics if m.success]
        
        return {
            'total_operations': len(metrics),
            'successful_operations': len(successful_metrics),
            'success_rate': len(successful_metrics) / len(metrics) if metrics else 0,
            'average_duration': sum(durations) / len(durations) if durations else 0,
            'min_duration': min(durations) if durations else 0,
            'max_duration': max(durations) if durations else 0,
            'total_duration': sum(durations) if durations else 0
        }


class DiagnosticTool:
    """诊断工具"""
    
    def __init__(self, logger: StructuredLogger, profiler: PerformanceProfiler):
        self.logger = logger
        self.profiler = profiler
    
    def diagnose_api_issues(self, time_window_minutes: int = 60) -> Dict[str, Any]:
        """诊断API问题"""
        since = datetime.now() - timedelta(minutes=time_window_minutes)
        
        # 获取错误日志
        error_logs = self.logger.get_logs(level='ERROR', since=since)
        warning_logs = self.logger.get_logs(level='WARNING', since=since)
        
        # 分析错误模式
        error_patterns = {}
        for log in error_logs:
            if log.error_details:
                error_type = log.error_details.get('exception_type', 'Unknown')
                error_patterns[error_type] = error_patterns.get(error_type, 0) + 1
        
        # 获取性能统计
        perf_stats = self.profiler.get_performance_stats(since=since)
        
        # 诊断结果
        diagnosis = {
            'time_window': f"{time_window_minutes} minutes",
            'error_count': len(error_logs),
            'warning_count': len(warning_logs),
            'error_patterns': error_patterns,
            'performance_stats': perf_stats,
            'recommendations': []
        }
        
        # 生成建议
        if len(error_logs) > 10:
            diagnosis['recommendations'].append("错误频率过高，建议检查API配置和网络连接")
        
        if perf_stats.get('success_rate', 1) < 0.8:
            diagnosis['recommendations'].append("成功率过低，建议启用备用端点")
        
        if perf_stats.get('average_duration', 0) > 30:
            diagnosis['recommendations'].append("响应时间过长，建议优化请求或增加超时设置")
        
        if 'rate_limit' in str(error_patterns):
            diagnosis['recommendations'].append("检测到速率限制错误，建议调整请求频率")
        
        return diagnosis
    
    def generate_health_report(self) -> str:
        """生成健康报告"""
        diagnosis = self.diagnose_api_issues()
        
        report = []
        report.append("🏥 AI API 健康诊断报告")
        report.append("=" * 50)
        report.append(f"📅 时间窗口: {diagnosis['time_window']}")
        report.append(f"❌ 错误数量: {diagnosis['error_count']}")
        report.append(f"⚠️ 警告数量: {diagnosis['warning_count']}")
        
        if diagnosis['error_patterns']:
            report.append("\n🔍 错误模式:")
            for error_type, count in diagnosis['error_patterns'].items():
                report.append(f"  {error_type}: {count} 次")
        
        perf_stats = diagnosis['performance_stats']
        if perf_stats:
            report.append(f"\n📊 性能统计:")
            report.append(f"  总操作数: {perf_stats.get('total_operations', 0)}")
            report.append(f"  成功率: {perf_stats.get('success_rate', 0):.2%}")
            report.append(f"  平均耗时: {perf_stats.get('average_duration', 0):.2f}s")
        
        if diagnosis['recommendations']:
            report.append(f"\n💡 建议:")
            for rec in diagnosis['recommendations']:
                report.append(f"  • {rec}")
        
        report.append("=" * 50)
        
        return "\n".join(report)


class DebugHelper:
    """调试助手"""
    
    def __init__(self, logger: StructuredLogger):
        self.logger = logger
        self.debug_mode = False
        self.trace_calls = False
    
    def enable_debug_mode(self):
        """启用调试模式"""
        self.debug_mode = True
        self.logger.info('DEBUG', "调试模式已启用")
    
    def disable_debug_mode(self):
        """禁用调试模式"""
        self.debug_mode = False
        self.logger.info('DEBUG', "调试模式已禁用")
    
    def enable_call_tracing(self):
        """启用调用跟踪"""
        self.trace_calls = True
        self.logger.info('DEBUG', "调用跟踪已启用")
    
    def disable_call_tracing(self):
        """禁用调用跟踪"""
        self.trace_calls = False
        self.logger.info('DEBUG', "调用跟踪已禁用")
    
    def trace_function(self, func: Callable) -> Callable:
        """函数跟踪装饰器"""
        def wrapper(*args, **kwargs):
            if self.trace_calls:
                self.logger.debug('TRACE', f"调用函数: {func.__name__}", 
                                context={'args': str(args), 'kwargs': str(kwargs)})
            
            try:
                result = func(*args, **kwargs)
                if self.trace_calls:
                    self.logger.debug('TRACE', f"函数返回: {func.__name__}", 
                                    context={'result_type': type(result).__name__})
                return result
            except Exception as e:
                if self.trace_calls:
                    self.logger.error('TRACE', f"函数异常: {func.__name__}", exception=e)
                raise
        
        return wrapper
    
    def dump_state(self, obj: Any, name: str = "object"):
        """转储对象状态"""
        if self.debug_mode:
            try:
                state = {}
                for attr in dir(obj):
                    if not attr.startswith('_'):
                        try:
                            value = getattr(obj, attr)
                            if not callable(value):
                                state[attr] = str(value)
                        except:
                            state[attr] = "<无法访问>"
                
                self.logger.debug('STATE_DUMP', f"对象状态: {name}", context=state)
            except Exception as e:
                self.logger.error('STATE_DUMP', f"状态转储失败: {name}", exception=e)
