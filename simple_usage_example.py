#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版AI API系统使用示例
演示如何使用简化后的系统进行基本的AI API调用
"""

import json
from model_config import get_model_config


def example_basic_api_call():
    """基础API调用示例"""
    print("🔹 基础API调用示例")
    print("-" * 50)
    
    # 获取配置实例
    config = get_model_config()
    
    # 准备测试消息
    messages = [
        {
            "role": "system",
            "content": "你是一个专业的数据分析师，请对用户提供的数据进行简要分析。"
        },
        {
            "role": "user",
            "content": "请分析以下游戏用户反馈：\n用户A: 游戏很好玩，但是有些卡顿\n用户B: 希望增加新功能\n用户C: 充值系统需要优化"
        }
    ]
    
    print("📞 执行基础API调用...")
    result = config.call_api("step1_analysis", messages)
    
    if result:
        print(f"✅ 调用成功，响应长度: {len(result)} 字符")
        print(f"📝 响应预览: {result[:200]}...")
        return result
    else:
        print("❌ 调用失败")
        return None


def example_html_generation():
    """HTML生成示例"""
    print("\n🔹 HTML生成示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 模拟分析数据
    analysis_data = {
        "E版-小R用户": {
            "ai_response": json.dumps({
                "sentiment_analysis": {
                    "positive_ratio": 0.6,
                    "negative_ratio": 0.3,
                    "neutral_ratio": 0.1
                },
                "key_topics": ["游戏体验", "充值优化", "新功能需求"],
                "summary": "小R用户整体满意度较高，主要关注游戏体验和功能更新"
            }, ensure_ascii=False),
            "metadata": {
                "total_comments": 150,
                "analyzed_comments": 150,
                "unique_users": 45
            }
        }
    }
    
    messages = [
        {
            "role": "system",
            "content": "你是一个HTML报告生成专家，请根据数据生成简洁的HTML报告。"
        },
        {
            "role": "user",
            "content": f"请根据以下分析数据生成一个简单的HTML报告：\n\n{json.dumps(analysis_data, ensure_ascii=False, indent=2)}"
        }
    ]
    
    print("🚀 执行HTML生成...")
    result = config.call_api("step2_html_generation", messages)
    
    if result:
        print(f"✅ HTML生成成功，响应长度: {len(result)} 字符")
        
        # 保存HTML文件
        with open("data/test_report.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("📁 HTML文件已保存到: data/test_report.html")
        
        return result
    else:
        print("❌ HTML生成失败")
        return None


def example_error_handling():
    """错误处理示例"""
    print("\n🔹 错误处理示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 测试超大请求（可能触发token限制）
    large_content = "这是一个测试数据。" * 1000  # 创建大量重复内容
    
    messages = [
        {
            "role": "system",
            "content": "你是一个数据分析师。"
        },
        {
            "role": "user",
            "content": f"请分析以下大量数据：\n{large_content}"
        }
    ]
    
    print("🧪 测试大请求处理...")
    result = config.call_api("step1_analysis", messages, max_retries=2)
    
    if result:
        if result.startswith("TOKEN_LIMIT_EXCEEDED"):
            print("⚠️ 检测到token限制，系统正确处理了错误")
            print(f"📊 错误信息: {result}")
        else:
            print(f"✅ 大请求处理成功，响应长度: {len(result)} 字符")
    else:
        print("❌ 大请求处理失败")


def example_configuration():
    """配置管理示例"""
    print("\n🔹 配置管理示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 显示当前配置
    print("📋 当前模型配置:")
    config.list_configs()
    
    # 获取特定步骤的参数
    print("\n🔍 步骤参数详情:")
    try:
        params1 = config.get_model_params("step1_analysis")
        params2 = config.get_model_params("step2_html_generation")
        
        print(f"  step1_analysis: {params1['model']}")
        print(f"  step2_html_generation: {params2['model']}")
        
    except Exception as e:
        print(f"❌ 参数获取失败: {e}")


def example_workflow_simulation():
    """工作流程模拟"""
    print("\n🔹 完整工作流程模拟")
    print("-" * 50)
    
    print("🚀 模拟完整的数据分析 → HTML生成工作流程")
    
    # 步骤1: 数据分析
    print("\n1️⃣ 步骤1: 数据分析")
    analysis_result = example_basic_api_call()
    
    if not analysis_result:
        print("❌ 数据分析失败，工作流程中断")
        return
    
    # 步骤2: HTML生成
    print("\n2️⃣ 步骤2: HTML生成")
    html_result = example_html_generation()
    
    if html_result:
        print("✅ 完整工作流程执行成功")
        print("📁 可以查看生成的文件: data/test_report.html")
    else:
        print("❌ HTML生成失败")


def test_system_health():
    """系统健康检查"""
    print("\n🔹 系统健康检查")
    print("-" * 50)
    
    try:
        config = get_model_config()
        
        # 检查配置
        print("✅ 配置系统正常")
        
        # 检查提供商
        providers = config.config.get('providers', {})
        print(f"✅ 配置了 {len(providers)} 个API提供商")
        
        # 检查模型
        models = config.config.get('models', {})
        print(f"✅ 配置了 {len(models)} 个模型步骤")
        
        # 检查客户端连接（不实际调用API）
        for provider_name in providers:
            try:
                client = config.get_client(provider_name)
                print(f"✅ {provider_name} 客户端初始化成功")
            except Exception as e:
                print(f"❌ {provider_name} 客户端初始化失败: {e}")
        
        print("🎉 系统健康检查完成")
        
    except Exception as e:
        print(f"❌ 系统健康检查失败: {e}")


def main():
    """主函数"""
    print("🚀 简化版AI API系统使用示例")
    print("=" * 80)
    
    # 确保data目录存在
    import os
    os.makedirs('data', exist_ok=True)
    
    try:
        # 运行各种示例
        test_system_health()
        example_configuration()
        example_basic_api_call()
        example_html_generation()
        example_error_handling()
        example_workflow_simulation()
        
        print("\n" + "=" * 80)
        print("🎉 所有示例执行完成！")
        print("\n💡 使用提示:")
        print("   - 系统已简化为使用单一Gemini API提供商")
        print("   - 保留了核心的重试机制和错误处理")
        print("   - 所有现有的调用方式保持兼容")
        print("   - 可以直接运行 python main.py 开始完整流程")
        
        print("\n📁 生成的文件:")
        if os.path.exists("data/test_report.html"):
            print("   - data/test_report.html (测试HTML报告)")
        
    except Exception as e:
        print(f"❌ 示例执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
