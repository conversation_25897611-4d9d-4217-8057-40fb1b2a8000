#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI API 优化系统使用示例
演示如何使用新的优化功能来提高API调用的可靠性和效率
"""

import time
from model_config import get_model_config


def example_basic_usage():
    """基础使用示例"""
    print("🔹 基础使用示例")
    print("-" * 50)
    
    # 获取配置实例
    config = get_model_config()
    
    # 准备测试消息
    messages = [
        {
            "role": "system",
            "content": "你是一个专业的数据分析师，请对用户提供的数据进行分析。"
        },
        {
            "role": "user",
            "content": "请分析以下游戏用户反馈数据：\n用户A: 游戏很好玩\n用户B: 有些卡顿\n用户C: 希望增加新功能"
        }
    ]
    
    # 基础API调用
    print("📞 执行基础API调用...")
    result = config.call_api("step1_analysis", messages, expected_format="json")
    
    if result:
        print(f"✅ 调用成功，响应长度: {len(result)} 字符")
        print(f"📝 响应预览: {result[:200]}...")
    else:
        print("❌ 调用失败")
    
    print()


def example_optimized_usage():
    """优化版使用示例"""
    print("🔹 优化版使用示例")
    print("-" * 50)
    
    config = get_model_config()
    
    messages = [
        {
            "role": "system",
            "content": "你是一个HTML生成专家，请根据数据生成完整的HTML报告。"
        },
        {
            "role": "user",
            "content": "请生成一个包含用户反馈分析的HTML报告，数据如下：..." + "测试数据" * 100
        }
    ]
    
    # 优化版API调用
    print("🚀 执行优化版API调用...")
    result = config.call_api_optimized(
        step="step2_html_generation",
        messages=messages,
        priority=3,  # 高优先级
        expected_format="html",
        auto_optimize=True  # 启用自动Token优化
    )
    
    if result:
        print(f"✅ 优化调用成功，响应长度: {len(result)} 字符")
    else:
        print("❌ 优化调用失败")
    
    print()


def example_enhanced_usage():
    """增强版使用示例（推荐）"""
    print("🔹 增强版使用示例（推荐）")
    print("-" * 50)
    
    config = get_model_config()
    
    # 启用调试模式以查看详细信息
    config.enable_debug_mode()
    
    messages = [
        {
            "role": "system",
            "content": "你是一个智能助手，请提供有用的回答。"
        },
        {
            "role": "user",
            "content": "请解释什么是人工智能，并给出一些应用例子。"
        }
    ]
    
    # 增强版API调用（集成所有优化功能）
    print("⚡ 执行增强版API调用...")
    result = config.call_api_enhanced(
        step="step1_analysis",
        messages=messages,
        priority=2,  # 高优先级
        expected_format="text",
        enable_fallback=True,  # 启用故障恢复
        enable_profiling=True  # 启用性能分析
    )
    
    if result:
        print(f"✅ 增强调用成功，响应长度: {len(result)} 字符")
        print(f"📝 响应预览: {result[:300]}...")
    else:
        print("❌ 增强调用失败")
    
    # 禁用调试模式
    config.disable_debug_mode()
    
    print()


def example_monitoring():
    """监控和诊断示例"""
    print("🔹 监控和诊断示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 查看系统健康状态
    print("🏥 系统健康状态:")
    config.print_system_health()
    
    # 查看性能指标
    print("\n📊 性能指标:")
    config.print_metrics()
    
    # 查看优化统计
    print("\n🎯 优化统计:")
    config.print_optimization_stats()
    
    # 获取诊断报告
    print("\n🔍 诊断报告:")
    diagnostic_report = config.get_diagnostic_report()
    print(diagnostic_report)
    
    print()


def example_error_handling():
    """错误处理示例"""
    print("🔹 错误处理示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 模拟可能导致错误的大请求
    large_messages = [
        {
            "role": "system",
            "content": "你是一个数据分析专家。"
        },
        {
            "role": "user",
            "content": "请分析以下大量数据：" + "大量测试数据" * 1000  # 可能超过Token限制
        }
    ]
    
    print("🧪 测试错误处理和恢复机制...")
    
    try:
        result = config.call_api_enhanced(
            step="step1_analysis",
            messages=large_messages,
            enable_fallback=True
        )
        
        if result:
            print(f"✅ 错误恢复成功，响应长度: {len(result)} 字符")
        else:
            print("❌ 所有恢复尝试都失败了")
            
    except Exception as e:
        print(f"❌ 发生异常: {str(e)}")
    
    # 查看错误统计
    metrics = config.get_metrics()
    error_counts = metrics['api_metrics']['error_counts']
    if error_counts:
        print(f"\n📈 错误统计: {error_counts}")
    
    print()


def example_log_management():
    """日志管理示例"""
    print("🔹 日志管理示例")
    print("-" * 50)
    
    config = get_model_config()
    
    # 获取最近的日志
    recent_logs = config.logger.get_logs(limit=5)
    print(f"📋 最近 {len(recent_logs)} 条日志:")
    for log in recent_logs:
        print(f"  {log.timestamp} [{log.level}] {log.component}: {log.message}")
    
    # 导出日志
    print("\n💾 导出日志...")
    log_file = config.export_logs("example_logs.json", "json")
    print(f"✅ 日志已导出到: {log_file}")
    
    print()


def comprehensive_example():
    """综合示例"""
    print("🔹 综合示例 - 完整工作流程")
    print("-" * 50)
    
    config = get_model_config()
    
    # 1. 系统初始化检查
    print("1️⃣ 系统初始化检查...")
    health = config.get_system_health()
    print(f"   系统状态: {'健康' if health else '异常'}")
    
    # 2. 执行多个API调用
    print("\n2️⃣ 执行多个API调用...")
    
    test_cases = [
        {
            "name": "数据分析",
            "step": "step1_analysis",
            "messages": [
                {"role": "system", "content": "你是数据分析师"},
                {"role": "user", "content": "分析用户反馈数据"}
            ]
        },
        {
            "name": "HTML生成",
            "step": "step2_html_generation", 
            "messages": [
                {"role": "system", "content": "你是HTML专家"},
                {"role": "user", "content": "生成报告页面"}
            ]
        }
    ]
    
    results = []
    for i, test_case in enumerate(test_cases, 1):
        print(f"   {i}. 测试 {test_case['name']}...")
        
        result = config.call_api_enhanced(
            step=test_case["step"],
            messages=test_case["messages"],
            priority=i,
            enable_fallback=True,
            enable_profiling=True
        )
        
        results.append({
            "name": test_case["name"],
            "success": result is not None,
            "length": len(result) if result else 0
        })
        
        print(f"      {'✅ 成功' if result else '❌ 失败'}")
    
    # 3. 生成完整报告
    print("\n3️⃣ 生成完整系统报告...")
    config.print_comprehensive_report()
    
    # 4. 总结
    print(f"\n4️⃣ 测试总结:")
    successful_tests = sum(1 for r in results if r["success"])
    print(f"   总测试数: {len(results)}")
    print(f"   成功数: {successful_tests}")
    print(f"   成功率: {successful_tests/len(results)*100:.1f}%")
    
    for result in results:
        status = "✅" if result["success"] else "❌"
        print(f"   {status} {result['name']}: {result['length']} 字符")


def main():
    """主函数"""
    print("🚀 AI API 优化系统使用示例")
    print("=" * 80)
    print()
    
    try:
        # 运行各种示例
        example_basic_usage()
        example_optimized_usage()
        example_enhanced_usage()
        example_monitoring()
        example_error_handling()
        example_log_management()
        comprehensive_example()
        
        print("🎉 所有示例执行完成！")
        print("\n💡 提示:")
        print("   - 在生产环境中推荐使用 call_api_enhanced() 方法")
        print("   - 定期检查系统健康状态和性能指标")
        print("   - 启用日志记录以便问题诊断")
        print("   - 根据实际需求调整重试策略和超时设置")
        
    except Exception as e:
        print(f"❌ 示例执行出错: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
