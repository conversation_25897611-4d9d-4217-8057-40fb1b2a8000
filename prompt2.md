# 游戏舆情日报自动生成提示词

## 任务描述
根据提供的JSON格式游戏舆情数据，将HTML模板中的占位符替换为相应的数据，生成完整的可视化日报。

## 数据处理规则

### 1. 基础数据提取 ({{变量名}})

#### 日期和综合指标
- `{{analysis_date}}`: 使用分析日期（昨天的日期），格式："YYYY年MM月DD日"
- `{{weighted_health_score}}`: 计算所有层级用户的加权健康度平均值，权重如下：
  - 超R用户：30%
  - 大R用户：25% 
  - 中R用户：20%
  - 小R用户：15%
  - 零氪用户：10%
- `{{total_messages}}`: 汇总所有层级的total_comments，格式化为千分位（如：347,200）
- `{{total_users}}`: 汇总所有层级的unique_users，格式化为千分位（如：18,216）
- `{{high_risk_count}}`: 统计所有风险alerts中severity为"高"的数量
- `{{negative_ratio}}`: 计算所有层级负面情绪的加权平均值，保留一位小数

#### 核心洞察
- `{{daily_summary}}`: 基于数据生成80-120字的核心洞察总结，重点突出：
  1. 最严重的问题（如数字屏蔽）
  2. 主要的体验痛点（如巨牙行动、洗练机制）
  3. 用户流失风险提示

- `{{insight_tags}}`: 生成3-5个洞察标签的HTML，格式：
```html
<span class="insight-tag negative">沟通体验严重受损</span>
<span class="insight-tag warning">付费意愿下降风险</span>
<span class="insight-tag negative">用户流失风险上升</span>
```
标签类型：`negative`（红色）、`warning`（橙色）、`positive`（绿色）

### 2. 热点话题处理 ({{hot_topics}})

合并所有层级的top_topics，选择讨论量最高的前3个话题，生成HTML：

```html
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#1</span>
        <span class="title">话题标题</span>
        <span class="impact-badge">讨论量</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 XX%</div>
        <div class="affected-levels">影响用户层级</div>
    </div>
    <div class="representative-quote">"代表性引言"</div>
</div>
```

### 3. 风险预警处理 ({{risk_alerts}})

汇总所有risk_alerts，按严重程度排序，生成HTML：

```html
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">风险类型</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
    </div>
    <div class="risk-description">风险描述</div>
</div>
```

### 4. 用户健康度处理 ({{user_health_levels}})

为每个用户层级生成健康度显示，用户层级顺序：超R、大R、中R、小R、零氪

```html
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">超R用户</div>
        <div class="user-count">XXX人</div>
    </div>
    <div class="health-indicator" data-score="XX">
        <span class="score">XX</span>
    </div>
    <div class="level-status">
        <span class="status-text">状态描述</span>
        <span class="trend-arrow">趋势箭头</span>
    </div>
</div>
```

状态描述规则：
- 80-100分：优秀 ⬆️
- 60-79分：良好 ➡️
- 40-59分：一般 ⬇️
- 0-39分：较差 ⬇️

### 5. 版本对比处理

#### E版本数据
- `{{e_version_health_score}}`: E版本用户的加权健康度
- `{{e_version_risk_count}}`: E版本高风险数量
- `{{e_version_hot_issue}}`: E版本最热门话题标题

#### D版本数据  
- `{{d_version_health_score}}`: D版本用户的加权健康度
- `{{d_version_risk_count}}`: D版本高风险数量
- `{{d_version_hot_issue}}`: D版本最热门话题标题

#### 版本差异 ({{version_differences}})
生成4-5个差异点的HTML列表：
```html
<li>E版用户对游戏机制抱怨更多，D版用户更关注资源获取</li>
<li>E版数字屏蔽问题更严重，影响用户沟通体验</li>
```

### 6. 词云生成 ({{word_cloud_bubbles}})

提取所有high_frequency_words，过滤掉无价值词汇（如"策划"、"客服"、"管理员"等通用称谓），选择频次最高的15-20个有价值词汇，生成词云HTML：

```html
<span class="word-bubble" data-sentiment="negative" data-frequency="300" style="font-size: 2rem;">数字屏蔽</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="250" style="font-size: 1.8rem;">巨牙行动</span>
```

字体大小规则：
- 频次500+: 2rem
- 频次300-499: 1.8rem  
- 频次200-299: 1.6rem
- 频次100-199: 1.4rem
- 频次50-99: 1.2rem
- 频次<50: 1rem

情感分类规则：
- negative: 屏蔽、打不出来、洗练、抽奖等负面词汇（注意：排除"策划"等无价值通用词汇）
- neutral: 巨牙、体力、密令、资源、军团等中性词汇  
- positive: 帮助、支持、感谢等正面词汇

词汇过滤规则：
- 需要排除的无价值词汇：策划、客服、管理员、官方等通用称谓
- 保留有舆情价值的游戏内容词汇和情绪表达词汇

### 7. 重点玩家处理 ({{key_players}})

从所有active_players中选择attention_level为"高"的前4名玩家：

```html
<div class="player-item">
    <div class="player-avatar">玩家名首字</div>
    <div class="player-info">
        <div class="player-name">玩家全名</div>
        <div class="player-level">用户层级</div>
    </div>
    <div class="player-stats">
        <span class="message-count">XX条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">关注原因</div>
</div>
```

情感标签规则：
- 负面：sentiment_tendency包含"负面"
- 中性：sentiment_tendency包含"中性"  
- 正面：sentiment_tendency包含"正面"

## 数据计算示例

### 加权健康度计算
```
加权健康度 = (超R健康度 × 0.3) + (大R健康度 × 0.25) + (中R健康度 × 0.2) + (小R健康度 × 0.15) + (零氪健康度 × 0.1)
```

### 用户数汇总
```
总用户数 = 各层级unique_users之和
总消息数 = 各层级total_comments之和
```

### 高风险统计
```
遍历所有risk_alerts，统计severity字段为"高"或"高风险"的条目数量
```

## 输出要求

1. **精确替换**：确保所有{{变量名}}占位符都被正确替换
2. **数据格式化**：大数字使用千分位分隔符
3. **HTML完整性**：生成的HTML标签必须完整且正确闭合
4. **样式一致性**：确保CSS类名与模板样式匹配
5. **数据准确性**：所有计算结果必须准确，引用数据必须来源可追溯

## 处理流程

1. **解析JSON数据**：提取各层级用户的ai_response和metadata
2. **数据汇总计算**：按权重计算综合指标
3. **内容生成**：为每个模块生成相应的HTML内容
4. **模板替换**：将所有占位符替换为实际数据
5. **质量检查**：确保HTML格式正确，数据逻辑合理

请严格按照以上规则处理数据并生成最终的HTML文件。

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏舆情可视化日报</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* 主色调 */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-accent: #EFF6FF;
            --accent-primary: #3B82F6;
            --accent-secondary: #10B981;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-light: #E5E7EB;
            --border-medium: #D1D5DB;
            
            /* 用户层级色彩 */
            --super-r: linear-gradient(135deg, #A855F7, #C084FC);
            --big-r: linear-gradient(135deg, #3B82F6, #60A5FA);
            --mid-r: linear-gradient(135deg, #10B981, #34D399);
            --small-r: linear-gradient(135deg, #F59E0B, #FBBF24);
            --zero-pay: linear-gradient(135deg, #8B5CF6, #A78BFA);
            
            /* 健康度色彩 */
            --health-excellent: #059669;
            --health-good: #0284C7;
            --health-warning: #EA580C;
            --health-critical: #DC2626;
            
            /* 风险等级色彩 */
            --risk-high: #DC2626;
            --risk-medium: #EA580C;
            --risk-low: #059669;
            
            /* 字体大小 */
            --font-title-main: 2.5rem;
            --font-title-sub: 2rem;
            --font-title-card: 1.5rem;
            --font-body: 1rem;
            --font-caption: 0.875rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'Microsoft YaHei', 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas:
                "overview overview overview"
                "topics risks health"
                "compare wordcloud players";
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 1.5rem;
            padding: 2rem;
            min-height: 100vh;
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: var(--border-medium);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: var(--font-title-card);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card h3 i {
            color: var(--accent-primary);
        }

        /* 主卡片：舆情总览 */
        .main-overview-card {
            grid-area: overview;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .title-section h1 {
            font-size: var(--font-title-main);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-section h1 i {
            color: var(--accent-primary);
        }

        .date-badge {
            background: var(--accent-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .health-score-large {
            display: flex;
            align-items: center;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            text-align: center;
            position: relative;
        }

        .score-value {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .score-label {
            font-size: var(--font-caption);
            margin-top: 0.25rem;
        }

        .global-metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            background: var(--bg-accent);
            transform: translateY(-1px);
        }

        .metric-item i {
            font-size: 1.5rem;
            color: var(--accent-primary);
            width: 40px;
            text-align: center;
        }

        .metric-content .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
        }

        .metric-content .label {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .core-insight {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-primary);
        }

        .core-insight h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .core-insight p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .insight-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .insight-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .insight-tag.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .insight-tag.warning {
            background: #FEF3C7;
            color: #92400E;
        }

        .insight-tag.positive {
            background: #DCFCE7;
            color: #166534;
        }

        /* 热点话题卡片 */
        .hot-topics-card {
            grid-area: topics;
        }

        .topics-ranking {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .topic-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-secondary);
            transition: all 0.3s ease;
        }

        .topic-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .topic-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .topic-header .rank {
            background: var(--accent-secondary);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-caption);
            font-weight: 600;
        }

        .topic-header .title {
            flex: 1;
            margin-left: 1rem;
            font-weight: 600;
        }

        .impact-badge {
            background: var(--bg-accent);
            color: var(--accent-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
        }

        .topic-details {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .negative-rate, .affected-levels {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .representative-quote {
            font-style: italic;
            color: var(--text-secondary);
            font-size: var(--font-caption);
            border-left: 2px solid var(--border-light);
            padding-left: 0.75rem;
        }

        /* 风险预警卡片 */
        .risk-radar-card {
            grid-area: risks;
        }

        .risk-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .risk-category {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--risk-medium);
        }

        .risk-category.severity-高 {
            border-left-color: var(--risk-high);
        }

        .risk-category.severity-中 {
            border-left-color: var(--risk-medium);
        }

        .risk-category.severity-低 {
            border-left-color: var(--risk-low);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .category-name {
            font-weight: 600;
            flex: 1;
        }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
            color: white;
            background: var(--risk-medium);
        }

        .severity-badge.severity-高 {
            background: var(--risk-high);
        }

        .severity-badge.severity-中 {
            background: var(--risk-medium);
        }

        .severity-badge.severity-低 {
            background: var(--risk-low);
        }

        .affected-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .level-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .risk-description {
            color: var(--text-secondary);
            font-size: var(--font-caption);
            line-height: 1.6;
        }

        /* 用户群体健康度卡片 */
        .user-health-card {
            grid-area: health;
        }

        .health-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .level-health-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .level-info {
            flex: 1;
        }

        .level-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-count {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .health-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.2rem;
        }

        .level-status {
            text-align: center;
        }

        .status-text {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            display: block;
        }

        .trend-arrow {
            font-size: 1.5rem;
            margin-top: 0.25rem;
        }

        /* 版本对比卡片 */
        .version-compare-card {
            grid-area: compare;
        }

        .compare-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .version-column {
            flex: 1;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .version-column h4 {
            margin-bottom: 1rem;
            color: var(--accent-primary);
            font-size: 1.2rem;
        }

        .version-metrics {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .version-metrics .health-score {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .version-metrics .risk-count,
        .version-metrics .hot-issue {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .vs-divider {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .key-differences {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
        }

        .key-differences h5 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .key-differences ul {
            list-style: none;
        }

        .key-differences li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
            color: var(--text-secondary);
            font-size: var(--font-caption);
        }

        /* 词云卡片 */
        .wordcloud-card {
            grid-area: wordcloud;
        }

        .word-cloud-container {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            min-height: 200px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .word-bubble {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .word-bubble[data-sentiment="negative"] {
            background: #FEE2E2;
            color: #991B1B;
        }

        .word-bubble[data-sentiment="neutral"] {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .word-bubble[data-sentiment="positive"] {
            background: #DCFCE7;
            color: #166534;
        }

        .word-bubble:hover {
            transform: scale(1.1);
        }

        .sentiment-legend {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-caption);
        }

        .legend-item:before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .legend-item.positive:before {
            background: #166534;
        }

        .legend-item.neutral:before {
            background: var(--accent-primary);
        }

        .legend-item.negative:before {
            background: #991B1B;
        }

        /* 重点关注玩家卡片 */
        .key-players-card {
            grid-area: players;
        }

        .players-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .player-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .player-info {
            flex: 1;
        }

        .player-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .player-level {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .player-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .message-count {
            font-weight: 600;
            color: var(--text-primary);
        }

        .sentiment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .sentiment-badge.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .sentiment-badge.neutral {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .sentiment-badge.positive {
            background: #DCFCE7;
            color: #166534;
        }

        .attention-reason {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            line-height: 1.5;
            max-width: 200px;
        }

        /* 健康度分数样式 */
        .health-indicator[data-score^="8"],
        .health-indicator[data-score^="9"],
        .score-circle[data-score^="8"],
        .score-circle[data-score^="9"] {
            background: var(--health-excellent);
        }

        .health-indicator[data-score^="6"],
        .health-indicator[data-score^="7"],
        .score-circle[data-score^="6"],
        .score-circle[data-score^="7"] {
            background: var(--health-good);
        }

        .health-indicator[data-score^="4"],
        .health-indicator[data-score^="5"],
        .score-circle[data-score^="4"],
        .score-circle[data-score^="5"] {
            background: var(--health-warning);
        }

        .health-indicator[data-score^="0"],
        .health-indicator[data-score^="1"],
        .health-indicator[data-score^="2"],
        .health-indicator[data-score^="3"],
        .score-circle[data-score^="0"],
        .score-circle[data-score^="1"],
        .score-circle[data-score^="2"],
        .score-circle[data-score^="3"] {
            background: var(--health-critical);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-areas:
                    "overview overview"
                    "topics risks"
                    "health compare"
                    "wordcloud players";
                grid-template-columns: 1fr 1fr;
            }

            .global-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas:
                    "overview"
                    "topics"
                    "risks"
                    "health"
                    "compare"
                    "wordcloud"
                    "players";
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .global-metrics-grid {
                grid-template-columns: 1fr;
            }

            .overview-header {
                flex-direction: column;
                gap: 1rem;
            }

            .compare-container {
                flex-direction: column;
            }

            .vs-divider {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 主卡片：舆情总览 -->
        <div class="main-overview-card card">
            <div class="overview-header">
                <div class="title-section">
                    <h1><i class="fas fa-gamepad"></i> 游戏舆情日报</h1>
                    <div class="date-badge">{{analysis_date}}</div>
                </div>
                <div class="health-score-large">
                    <div class="score-circle" data-score="{{weighted_health_score}}">
                        <span class="score-value">{{weighted_health_score}}</span>
                        <span class="score-label">综合健康度</span>
                    </div>
                </div>
            </div>
            
            <div class="global-metrics-grid">
                <div class="metric-item">
                    <i class="fas fa-comments"></i>
                    <div class="metric-content">
                        <span class="value">{{total_messages}}</span>
                        <span class="label">总消息数</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-users"></i>
                    <div class="metric-content">
                        <span class="value">{{total_users}}</span>
                        <span class="label">活跃用户</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="metric-content">
                        <span class="value">{{high_risk_count}}</span>
                        <span class="label">高风险警报</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="metric-content">
                        <span class="value">{{negative_ratio}}%</span>
                        <span class="label">负面情绪占比</span>
                    </div>
                </div>
            </div>
            
            <div class="core-insight">
                <h3><i class="fas fa-lightbulb"></i> 今日核心洞察</h3>
                <p>{{daily_summary}}</p>
                <div class="insight-tags">
                    {{insight_tags}}
                </div>
            </div>
        </div>

        <!-- 热点话题卡片 -->
        <div class="hot-topics-card card">
            <h3><i class="fas fa-fire"></i> 全服热点话题</h3>
            <div class="topics-ranking">
                {{hot_topics}}
            </div>
        </div>

        <!-- 风险预警雷达 -->
        <div class="risk-radar-card card">
            <h3><i class="fas fa-exclamation-triangle"></i> 风险预警雷达</h3>
            <div class="risk-matrix">
                {{risk_alerts}}
            </div>
        </div>

        <!-- 用户群体健康度 -->
        <div class="user-health-card card">
            <h3><i class="fas fa-users"></i> 用户群体健康度</h3>
            <div class="health-matrix">
                {{user_health_levels}}
            </div>
        </div>

        <!-- 版本对比分析 -->
        <div class="version-compare-card card">
            <h3><i class="fas fa-balance-scale"></i> 版本对比分析</h3>
            <div class="compare-container">
                <div class="version-column">
                    <h4>E版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">{{e_version_health_score}}</div>
                        <div class="risk-count">{{e_version_risk_count}}个风险点</div>
                        <div class="hot-issue">{{e_version_hot_issue}}</div>
                    </div>
                </div>
                <div class="vs-divider">VS</div>
                <div class="version-column">
                    <h4>D版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">{{d_version_health_score}}</div>
                        <div class="risk-count">{{d_version_risk_count}}个风险点</div>
                        <div class="hot-issue">{{d_version_hot_issue}}</div>
                    </div>
                </div>
            </div>
            <div class="key-differences">
                <h5>关键差异</h5>
                <ul>
                    {{version_differences}}
                </ul>
            </div>
        </div>

        <!-- 舆情词云分析 -->
        <div class="wordcloud-card card">
            <h3><i class="fas fa-tags"></i> 舆情词云分析</h3>
            <div class="word-cloud-container">
                {{word_cloud_bubbles}}
            </div>
            <div class="sentiment-legend">
                <span class="legend-item positive">正面词汇</span>
                <span class="legend-item neutral">中性词汇</span>
                <span class="legend-item negative">负面词汇</span>
            </div>
        </div>

        <!-- 重点关注玩家 -->
        <div class="key-players-card card">
            <h3><i class="fas fa-crown"></i> 重点关注玩家</h3>
            <div class="players-list">
                {{key_players}}
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为词云添加点击效果
            const wordBubbles = document.querySelectorAll('.word-bubble');
            wordBubbles.forEach(bubble => {
                bubble.addEventListener('click', function() {
                    const sentiment = this.getAttribute('data-sentiment');
                    const frequency = this.getAttribute('data-frequency') || Math.floor(Math.random() * 100) + 1;
                    alert(`词汇: ${this.textContent}\n情感倾向: ${sentiment}\n出现频次: ${frequency}`);
                });
            });

            // 为健康度指示器添加悬停效果
            const healthIndicators = document.querySelectorAll('.health-indicator, .score-circle');
            healthIndicators.forEach(indicator => {
                indicator.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });
                
                indicator.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 为卡片添加轻微的3D效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 20;
                    const rotateY = (centerX - x) / 20;
                    
                    this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-2px)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                });
            });
        });
    </script>
</body>
</html>

## 重要提示

请严格按照以下要求输出：

1. **只输出完整的HTML代码**，不要添加任何解释性文字
2. **不要使用markdown代码块标记** (如 ```html 或 ```)
3. **直接从 <!DOCTYPE html> 开始输出**
4. **确保HTML结构完整**，包含完整的 </html> 结尾
5. **所有占位符都必须替换为实际数据**

输出格式要求：直接输出纯HTML代码，无任何前缀或后缀说明。