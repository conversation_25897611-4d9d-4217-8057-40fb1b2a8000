# -*- coding: utf-8 -*-
"""
简化版模型配置管理
专注于核心AI API调用功能，移除复杂的负载均衡和备用端点
"""

from openai import OpenAI
import json
import os
import time
import re
from typing import Dict, Optional, Any


class ModelConfig:
    """简化版模型配置管理类"""
    
    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.clients = {}  # 缓存客户端实例
        
    def _load_config(self) -> Dict[str, Any]:
        """加载模型配置"""
        default_config = {
            "providers": {
                "gemini": {
                    "api_key": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
                    "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"
                }
            },
            "models": {
                "step1_analysis": {
                    "provider": "gemini",
                    "model": "gemini-2.5-flash-preview-05-20",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95,
                    "reasoning_effort": "medium"
                },
                "step2_html_generation": {
                    "provider": "gemini",
                    "model": "gemini-2.5-pro-preview-06-05",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_configs(default_config, config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self._save_config(default_config)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            return default_config
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_client(self, provider: str) -> OpenAI:
        """获取指定提供商的OpenAI客户端"""
        if provider not in self.clients:
            provider_config = self.config["providers"][provider]
            self.clients[provider] = OpenAI(
                api_key=provider_config["api_key"],
                base_url=provider_config["base_url"],
                timeout=120.0  # 设置2分钟超时
            )
        
        return self.clients[provider]
    
    def get_model_params(self, step: str) -> Dict[str, Any]:
        """获取指定步骤的模型参数"""
        if step not in self.config["models"]:
            raise ValueError(f"未找到步骤 '{step}' 的配置")
        
        return self.config["models"][step].copy()
    
    def call_api(self, step: str, messages: list, max_retries: int = 3) -> Optional[str]:
        """统一的API调用接口 - 简化版"""
        params = self.get_model_params(step)
        provider = params.get("provider", "gemini")
        client = self.get_client(provider)
        
        # 从参数中提取OpenAI API需要的参数
        api_params = {
            "model": params["model"],
            "messages": messages,
            "temperature": params.get("temperature", 0.3),
            "max_tokens": params.get("max_tokens", 8000),
            "top_p": params.get("top_p", 0.95)
        }
        
        # Gemini支持reasoning_effort参数
        if "reasoning_effort" in params and provider == "gemini":
            api_params["reasoning_effort"] = params["reasoning_effort"]
        
        for attempt in range(max_retries):
            try:
                model = params["model"]
                print(f"🤖 调用 {model} API (步骤:{step}, 尝试 {attempt + 1}/{max_retries})...")
                
                # 执行API调用
                response_content = self._execute_api_call(api_params, client)
                
                if response_content:
                    print(f"✅ API调用成功，响应长度: {len(response_content)} 字符")
                    return response_content.strip()
                else:
                    print(f"❌ API返回空响应")
                    
            except Exception as e:
                error_str = str(e)
                print(f"❌ API调用失败 (尝试 {attempt + 1}/{max_retries}): {error_str}")
                
                # 检查是否为token超限错误
                if "maximum context length" in error_str and "tokens" in error_str:
                    # 提取token信息并返回标准格式
                    current_match = re.search(r'you requested about (\d+) tokens', error_str)
                    max_match = re.search(r'maximum context length is (\d+) tokens', error_str)
                    
                    if current_match and max_match:
                        current_tokens = int(current_match.group(1))
                        max_tokens = int(max_match.group(1))
                        print(f"🎯 检测到token超限: 当前{current_tokens:,}, 最大{max_tokens:,}")
                        return f"TOKEN_LIMIT_EXCEEDED:{current_tokens}:{max_tokens}"
                
                if attempt < max_retries - 1:
                    # 根据错误类型确定等待时间
                    if "429" in error_str or "rate limit" in error_str.lower():
                        wait_time = 60  # 固定等待1分钟
                        print(f"⏰ 检测到速率限制，等待 {wait_time} 秒后重试...")
                    elif "503" in error_str or "502" in error_str or "server" in error_str.lower():
                        wait_time = min(30, 5 * (2 ** attempt))
                        print(f"⏰ 检测到服务器错误，等待 {wait_time} 秒后重试...")
                    elif "connection" in error_str.lower() or "timeout" in error_str.lower():
                        wait_time = min(15, 3 * (2 ** attempt))
                        print(f"⏰ 检测到连接错误，等待 {wait_time} 秒后重试...")
                    else:
                        wait_time = min(20, 2 ** attempt)
                        print(f"⏰ 其他错误，等待 {wait_time} 秒后重试...")
                    
                    time.sleep(wait_time)
                else:
                    print(f"❌ API调用最终失败，已达到最大重试次数")
        
        return None
    
    def _execute_api_call(self, api_params: dict, client) -> Optional[str]:
        """执行实际的API调用"""
        try:
            # 添加流式输出参数
            api_params["stream"] = True
            
            response = client.chat.completions.create(**api_params)
            
            # 处理流式响应
            full_response = ""
            print("📝 AI响应处理中...", end="", flush=True)
            
            response_start_time = time.time()
            total_timeout = 600  # 10分钟总超时
            last_content_time = time.time()
            content_timeout = 60  # 60秒无新内容超时
            
            thinking_dots = 0
            last_thinking_update = time.time()
            thinking_interval = 10
            thinking_warned = False
            content_started = False
            
            chunk_count = 0
            for chunk in response:
                chunk_count += 1
                current_time = time.time()
                elapsed = int(current_time - response_start_time)
                
                # 检查总响应时间是否超时
                if current_time - response_start_time > total_timeout:
                    print(f"\n⏰ 总响应超时 ({total_timeout//60}分钟)")
                    raise TimeoutError(f"总响应超时({total_timeout//60}分钟)")
                
                # 检查内容接收超时
                if full_response and current_time - last_content_time > content_timeout:
                    print(f"\n⏰ 内容传输中断 ({content_timeout}秒无新内容)")
                    raise TimeoutError(f"内容传输超时({content_timeout}秒)")
                
                # 显示思考进度
                if not content_started:
                    if elapsed > 180 and not thinking_warned:
                        print(f"\n💭 AI正在深度思考中，请耐心等待...")
                        thinking_warned = True
                    
                    if current_time - last_thinking_update > thinking_interval:
                        thinking_dots = (thinking_dots + 1) % 4
                        dots = "." * thinking_dots + " " * (3 - thinking_dots)
                        
                        if elapsed < 60:
                            print(f"\r📝 AI思考中{dots} ({elapsed}s)", end="", flush=True)
                        else:
                            minutes = elapsed // 60
                            seconds = elapsed % 60
                            print(f"\r📝 AI深度思考中{dots} ({minutes}m{seconds}s)", end="", flush=True)
                        
                        last_thinking_update = current_time
                
                # 处理chunk数据
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]
                    
                    if hasattr(choice, 'delta') and choice.delta:
                        delta_content = getattr(choice.delta, 'content', None)
                        if delta_content:
                            if not content_started:
                                elapsed_final = int(current_time - response_start_time)
                                if elapsed_final < 60:
                                    print(f"\r✨ AI思考完成({elapsed_final}s)，正在接收响应...")
                                else:
                                    minutes = elapsed_final // 60
                                    seconds = elapsed_final % 60
                                    print(f"\r✨ AI思考完成({minutes}m{seconds}s)，正在接收响应...")
                                content_started = True
                            
                            full_response += delta_content
                            last_content_time = current_time
                    
                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        if choice.finish_reason == 'stop':
                            break
                        elif choice.finish_reason == 'length':
                            print(f"\n⚠️ 响应因长度限制而截断")
                            break
                        elif choice.finish_reason == 'content_filter':
                            print(f"\n⚠️ 响应被内容过滤器拦截")
                            break
                        else:
                            print(f"\n⚠️ 响应异常结束: {choice.finish_reason}")
                            break
            
            # 响应完成处理
            if full_response:
                total_elapsed = int(time.time() - response_start_time)
                if total_elapsed < 60:
                    print(f"\r✅ 响应完成({total_elapsed}s)，内容长度: {len(full_response)} 字符")
                else:
                    minutes = total_elapsed // 60
                    seconds = total_elapsed % 60
                    print(f"\r✅ 响应完成({minutes}m{seconds}s)，内容长度: {len(full_response)} 字符")
                return full_response
            else:
                # 尝试非流式调用作为备用
                if chunk_count > 0:
                    print(f"\n🔄 尝试非流式调用作为备用方案...")
                    api_params_backup = api_params.copy()
                    api_params_backup["stream"] = False
                    
                    backup_response = client.chat.completions.create(**api_params_backup)
                    
                    if backup_response.choices and len(backup_response.choices) > 0:
                        backup_content = backup_response.choices[0].message.content
                        if backup_content:
                            print(f"✅ 非流式备用调用成功，内容长度: {len(backup_content)} 字符")
                            return backup_content
                
                return None
                
        except Exception as e:
            print(f"\n❌ API调用异常: {str(e)}")
            raise e
    
    def update_config(self, step: str, params: Dict[str, Any]):
        """更新配置"""
        self.config["models"][step] = params
        self._save_config(self.config)
        print(f"✅ 已更新配置: {step}")
    
    def list_configs(self):
        """列出所有配置"""
        print("📋 当前模型配置:")
        for step, step_config in self.config["models"].items():
            model = step_config.get("model", "未设置")
            max_tokens = step_config.get("max_tokens", "未设置")
            reasoning = step_config.get("reasoning_effort", "N/A")
            print(f"  {step}: {model} (max_tokens: {max_tokens}, reasoning_effort: {reasoning})")


# 全局配置实例
model_config = ModelConfig()


def get_model_config() -> ModelConfig:
    """获取全局模型配置实例"""
    return model_config
