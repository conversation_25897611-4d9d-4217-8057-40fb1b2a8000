# -*- coding: utf-8 -*-
"""企业微信机器人推送模块 - 支持文件、图片和文本消息推送"""

import os
import requests
import json
import base64
import hashlib
from pathlib import Path

class WeComBot:
    """企业微信机器人"""
    
    def __init__(self, webhook_url=None):
        """
        初始化企业微信机器人
        
        Args:
            webhook_url: 机器人webhook地址，如果为None则从环境变量获取
        """
        self.webhook_url = webhook_url or os.getenv('WECOM_BOT_WEBHOOK')
        if not self.webhook_url:
            print("⚠️  警告: 未设置企业微信机器人webhook地址")
            print("   请设置环境变量 WECOM_BOT_WEBHOOK 或在初始化时传入webhook_url")
    
    def send_text(self, content, mentioned_list=None, mentioned_mobile_list=None):
        """
        发送文本消息
        
        Args:
            content: 消息内容
            mentioned_list: @的用户列表（userid）
            mentioned_mobile_list: @的用户手机号列表
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置webhook地址")
            return False
            
        data = {
            "msgtype": "text",
            "text": {
                "content": content
            }
        }
        
        # 添加@用户
        if mentioned_list or mentioned_mobile_list:
            data["text"]["mentioned_list"] = mentioned_list or []
            data["text"]["mentioned_mobile_list"] = mentioned_mobile_list or []
        
        return self._send_request(data)
    
    def send_markdown(self, content):
        """
        发送Markdown消息
        
        Args:
            content: Markdown内容
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置webhook地址")
            return False
            
        data = {
            "msgtype": "markdown",
            "markdown": {
                "content": content
            }
        }
        
        return self._send_request(data)
    
    def send_image(self, image_path):
        """
        发送图片消息
        
        Args:
            image_path: 图片文件路径
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置webhook地址")
            return False
            
        if not os.path.exists(image_path):
            print(f"❌ 错误: 图片文件不存在 {image_path}")
            return False
        
        # 检查文件大小（企业微信限制2MB）
        file_size = os.path.getsize(image_path)
        file_size_mb = file_size / (1024 * 1024)
        
        if file_size > 2 * 1024 * 1024:  # 2MB限制
            print(f"❌ 错误: 图片文件过大 {file_size_mb:.2f}MB，超过2MB限制")
            return False
        
        print(f"📤 正在发送图片...")
        print(f"   📄 文件: {image_path}")
        print(f"   📊 大小: {file_size_mb:.2f} MB")
        
        try:
            # 读取图片并转换为base64
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            base64_data = base64.b64encode(image_data).decode('utf-8')
            md5_hash = hashlib.md5(image_data).hexdigest()
            
            data = {
                "msgtype": "image",
                "image": {
                    "base64": base64_data,
                    "md5": md5_hash
                }
            }
            
            return self._send_request(data)
            
        except Exception as e:
            print(f"❌ 读取图片文件失败: {e}")
            return False
    
    def send_file(self, file_path):
        """
        发送文件消息
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置webhook地址")
            return False
            
        if not os.path.exists(file_path):
            print(f"❌ 错误: 文件不存在 {file_path}")
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)
        
        # 企业微信文件大小限制（通常是20MB）
        if file_size > 20 * 1024 * 1024:
            print(f"❌ 错误: 文件过大 {file_size_mb:.2f}MB，超过20MB限制")
            return False
        
        print(f"📤 正在发送文件...")
        print(f"   📄 文件: {file_path}")
        print(f"   📊 大小: {file_size_mb:.2f} MB")
        
        try:
            # 读取文件并转换为base64
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            base64_data = base64.b64encode(file_data).decode('utf-8')
            md5_hash = hashlib.md5(file_data).hexdigest()
            
            data = {
                "msgtype": "file",
                "file": {
                    "base64": base64_data,
                    "md5": md5_hash,
                    "filename": os.path.basename(file_path)
                }
            }
            
            return self._send_request(data)
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            return False
    
    def send_news(self, articles):
        """
        发送图文消息
        
        Args:
            articles: 图文消息列表，每个元素包含title, description, url, picurl
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置webhook地址")
            return False
            
        data = {
            "msgtype": "news",
            "news": {
                "articles": articles
            }
        }
        
        return self._send_request(data)
    
    def _send_request(self, data):
        """
        发送HTTP请求到企业微信机器人
        
        Args:
            data: 请求数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            headers = {'Content-Type': 'application/json'}
            response = requests.post(
                self.webhook_url,
                data=json.dumps(data, ensure_ascii=False).encode('utf-8'),
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('errcode') == 0:
                    print("✅ 消息发送成功!")
                    return True
                else:
                    error_msg = result.get('errmsg', '未知错误')
                    print(f"❌ 发送失败: {error_msg}")
                    return False
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return False
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时")
            return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求异常: {e}")
            return False
        except Exception as e:
            print(f"❌ 发送消息异常: {e}")
            return False

class SentimentReportBot(WeComBot):
    """游戏舆情报告推送机器人"""
    
    WEBHOOK_KEY = "c453c302-149e-4178-9ffd-e79cb25bbd37" # 企业微信机器人Webhook Key

    def __init__(self):
        """初始化舆情报告机器人，使用指定的webhook地址"""
        # 完整的Webhook URL，优先使用硬编码的KEY
        webhook_url = f"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key={self.WEBHOOK_KEY}"
        super().__init__(webhook_url=webhook_url)
    
    def send_daily_report(self, html_file=None, image_file=None, analysis_summary=None):
        """
        发送每日舆情报告 - 只推送图片
        
        Args:
            html_file: HTML报告文件路径（保留兼容性，但不使用）
            image_file: 报告图片文件路径
            analysis_summary: 分析摘要信息（保留兼容性，但不使用）
            
        Returns:
            bool: 发送是否成功
        """
        if not self.webhook_url:
            print("❌ 错误: 未配置企业微信机器人webhook地址")
            print("   请设置环境变量 WECOM_BOT_WEBHOOK")
            return False
        
        # 只发送图片
        if image_file and os.path.exists(image_file):
            print(f"📤 正在推送游戏舆情日报图片...")
            if self.send_image(image_file):
                print("✅ 企业微信图片推送成功!")
                return True
            else:
                print("❌ 企业微信图片推送失败")
                return False
        else:
            print("❌ 错误: 图片文件不存在，无法推送")
            print(f"   期望的图片文件: {image_file}")
            return False
    
    def _send_summary_text(self, analysis_summary):
        """发送分析摘要文本"""
        try:
            # 构建摘要文本
            summary_text = self._format_summary_text(analysis_summary)
            return self.send_markdown(summary_text)
        except Exception as e:
            print(f"❌ 发送摘要文本失败: {e}")
            return False
    
    def _format_summary_text(self, analysis_summary):
        """格式化分析摘要文本"""
        from datetime import datetime
        
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 基础信息
        total_users = analysis_summary.get('total_users', 0)
        total_messages = analysis_summary.get('total_messages', 0)
        versions = analysis_summary.get('versions', {})
        user_levels = analysis_summary.get('user_levels', set())
        has_errors = analysis_summary.get('has_errors', False)
        
        # 构建Markdown格式的消息
        text = f"""# 🎮 游戏舆情日报
        
**📅 报告时间:** {current_time}

## 📊 数据概览
- **👥 总用户数:** {total_users:,}
- **💬 总评论数:** {total_messages:,}
- **🎯 用户层级:** {', '.join(sorted(user_levels)) if user_levels else '无'}

## 🎮 游戏版本分析
"""
        
        for version, levels in versions.items():
            if levels:
                text += f"- **{version}:** {', '.join(levels)}\n"
        
        if has_errors:
            text += "\n⚠️ **注意:** 部分数据分析出现异常，请检查详细报告"
        
        text += f"\n---\n*由游戏舆情分析系统自动生成*"
        
        return text

def test_wecom_bot():
    """测试企业微信机器人功能"""
    print("🧪 测试企业微信机器人功能")
    
    # 创建机器人实例（使用硬编码的webhook地址）
    bot = SentimentReportBot()
    
    # 检查是否正确初始化
    if not bot.webhook_url:
        print("❌ 机器人初始化失败: webhook地址为空")
        return False
    
    print(f"📡 使用Webhook: {bot.webhook_url}")
    
    # 测试发送文本消息
    test_message = f"🤖 机器人测试消息\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    success = bot.send_text(test_message)
    
    if success:
        print("✅ 企业微信机器人测试成功!")
        return True
    else:
        print("❌ 企业微信机器人测试失败!")
        return False

if __name__ == "__main__":
    from datetime import datetime
    test_wecom_bot() 