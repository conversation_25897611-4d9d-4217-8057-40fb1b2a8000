# -*- coding: utf-8 -*-
"""
统一模型配置管理
支持为不同步骤和用户层级配置不同的AI模型
"""

from openai import OpenAI
import json
import os
import time
import signal
from datetime import datetime
from typing import Dict, Optional, Any
from api_monitoring import (
    APIMetrics, ErrorType, ErrorClassifier, ResponseValidator,
    CircuitBreaker, AdaptiveRetryStrategy
)
from token_optimizer import (
    TokenEstimator, TokenUsage, RequestMetadata, RequestQueue, LoadBalancer
)
from fault_recovery import (
    BackupManager, BackupEndpoint, ServiceStatus, FallbackStrategy
)
from logging_system import (
    StructuredLogger, PerformanceProfiler, DiagnosticTool, DebugHelper
)


class ModelConfig:
    """模型配置管理类 - 集成智能监控和重试系统"""

    def __init__(self, config_file: str = "model_config.json"):
        self.config_file = config_file
        self.config = self._load_config()
        self.clients = {}  # 缓存不同的客户端实例

        # 初始化监控组件
        self.metrics = APIMetrics()
        self.error_classifier = ErrorClassifier()
        self.response_validator = ResponseValidator()
        self.circuit_breaker = CircuitBreaker()
        self.retry_strategy = AdaptiveRetryStrategy()

        # 初始化优化组件
        self.token_estimator = TokenEstimator()
        self.request_queue = RequestQueue()
        self.load_balancer = LoadBalancer()

        # 初始化故障恢复组件
        self.backup_manager = BackupManager()
        self._setup_backup_endpoints()

        # 初始化日志和调试组件
        self.logger = StructuredLogger("ai_api_system")
        self.profiler = PerformanceProfiler(self.logger)
        self.diagnostic_tool = DiagnosticTool(self.logger, self.profiler)
        self.debug_helper = DebugHelper(self.logger)

        # 记录初始化完成
        self.logger.info('SYSTEM', "AI API系统初始化完成",
                        context={'config_file': self.config_file})
        
    def _load_config(self) -> Dict[str, Any]:
        """加载模型配置"""
        default_config = {
            "providers": {
                "openrouter": {
                    "api_key": "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",
                    "base_url": "https://openrouter.ai/api/v1"
                },
                "gemini": {
                    "api_key": "AIzaSyB0XJIM5aXH40t_0ogbLqzDpsa-n2-LNhA",
                    "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"
                }
            },
            "models": {
                "step1_analysis": {
                    "provider": "gemini",
                    "model": "gemini-2.5-flash-preview-05-20",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95,
                    "reasoning_effort": "medium"
                },
                "step2_html_generation": {
                    "provider": "gemini",
                    "model": "gemini-2.5-pro-preview-06-05",
                    "temperature": 0.7,
                    "max_tokens": 65536,
                    "top_p": 0.95
                }
            }
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置和用户配置
                return self._merge_configs(default_config, config)
            except Exception as e:
                print(f"⚠️ 配置文件加载失败，使用默认配置: {e}")
                return default_config
        else:
            # 创建默认配置文件
            self._save_config(default_config)
            print(f"✅ 已创建默认配置文件: {self.config_file}")
            return default_config
    
    def _merge_configs(self, default: Dict, user: Dict) -> Dict:
        """递归合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, config: Dict[str, Any]):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def get_client(self, provider: str) -> OpenAI:
        """获取指定提供商的OpenAI客户端"""
        if provider not in self.clients:
            provider_config = self.config["providers"][provider]
            self.clients[provider] = OpenAI(
                api_key=provider_config["api_key"],
                base_url=provider_config["base_url"],
                timeout=120.0  # 设置2分钟超时
            )
        
        return self.clients[provider]
    
    def get_model_params(self, step: str) -> Dict[str, Any]:
        """获取指定步骤的模型参数"""
        if step not in self.config["models"]:
            raise ValueError(f"未找到步骤 '{step}' 的配置")
        
        return self.config["models"][step].copy()
    
    def call_api(self, step: str, messages: list, max_retries: int = None,
                 expected_format: str = None) -> Optional[str]:
        """统一的API调用接口 - 集成智能监控和重试"""

        # 记录API调用开始
        self.logger.info('API_CALL', f"开始API调用",
                        context={'step': step, 'expected_format': expected_format})

        # 检查熔断器状态
        if not self.circuit_breaker.can_execute():
            self.logger.warning('CIRCUIT_BREAKER', "熔断器开启，跳过API调用")
            print(f"🔴 熔断器开启，跳过API调用")
            return None

        params = self.get_model_params(step)
        provider = params.get("provider", "gemini")
        client = self.get_client(provider)

        # 从参数中提取OpenAI API需要的参数
        api_params = {
            "model": params["model"],
            "messages": messages,
            "temperature": params.get("temperature", 0.3),
            "max_tokens": params.get("max_tokens", 8000),
            "top_p": params.get("top_p", 0.95)
        }

        # OpenRouter不支持reasoning_effort参数，只有直连Gemini支持
        if "reasoning_effort" in params and provider == "gemini":
            api_params["reasoning_effort"] = params["reasoning_effort"]

        # 记录API调用开始
        call_start_time = time.time()
        self.metrics.total_calls += 1

        last_error = None
        last_response = None

        # 使用自适应重试策略
        attempt = 0
        while attempt < (max_retries or 10):  # 默认最大10次重试
            attempt += 1

            try:
                model = params["model"]
                print(f"🤖 调用 {model} API (步骤:{step}, 尝试 {attempt})")

                # 执行API调用
                response_content = self._execute_api_call(api_params, client)

                if response_content:
                    # 验证响应
                    validation = self.response_validator.validate_response(
                        response_content, expected_format, context=step
                    )

                    if validation['is_valid']:
                        # 成功处理
                        call_duration = time.time() - call_start_time
                        self.metrics.successful_calls += 1
                        self.metrics.total_response_time += call_duration
                        self.circuit_breaker.record_success()

                        print(f"✅ API调用成功 (耗时: {call_duration:.1f}s, 置信度: {validation['confidence']:.2f})")
                        return response_content.strip()
                    else:
                        print(f"⚠️ 响应验证失败: {', '.join(validation['issues'])}")
                        last_response = response_content
                        last_error = f"Response validation failed: {validation['issues']}"
                else:
                    self.metrics.empty_responses += 1
                    last_error = "Empty response"
                    print(f"❌ API返回空响应")

            except Exception as e:
                last_error = str(e)
                print(f"❌ API调用异常: {last_error}")

            # 错误分类和重试决策
            error_type = self.error_classifier.classify_error(last_error, last_response)

            # 更新错误统计
            error_name = error_type.value
            self.metrics.error_counts[error_name] = self.metrics.error_counts.get(error_name, 0) + 1

            # 判断是否应该重试
            if not self.retry_strategy.should_retry(error_type, attempt):
                print(f"🛑 不再重试: {error_type.value}")
                break

            # 计算等待时间
            retry_config = self.retry_strategy.get_retry_config(error_type)
            delay = self.retry_strategy.calculate_delay(attempt, retry_config)

            if delay > 0:
                print(f"⏰ 等待 {delay:.1f} 秒后重试 ({error_type.value})")
                time.sleep(delay)

        # 所有重试都失败了
        self.metrics.failed_calls += 1
        self.circuit_breaker.record_failure()

        print(f"❌ API调用最终失败 (尝试 {attempt} 次): {last_error}")
        return None

    def _execute_api_call(self, api_params: dict, client) -> Optional[str]:
        """执行实际的API调用"""
        try:
            # 添加流式输出参数
            api_params["stream"] = True

            response = client.chat.completions.create(**api_params)

            # 处理流式响应
            full_response = ""
            print("📝 AI响应处理中...", end="", flush=True)

            response_start_time = time.time()
            total_timeout = 600  # 10分钟总超时
            last_content_time = time.time()
            content_timeout = 60  # 60秒无新内容超时

            thinking_dots = 0
            last_thinking_update = time.time()
            thinking_interval = 10
            thinking_warned = False
            content_started = False

            chunk_count = 0
            for chunk in response:
                chunk_count += 1
                current_time = time.time()
                elapsed = int(current_time - response_start_time)

                # 检查总响应时间是否超时
                if current_time - response_start_time > total_timeout:
                    print(f"\n⏰ 总响应超时 ({total_timeout//60}分钟)")
                    raise TimeoutError(f"总响应超时({total_timeout//60}分钟)")

                # 检查内容接收超时
                if full_response and current_time - last_content_time > content_timeout:
                    print(f"\n⏰ 内容传输中断 ({content_timeout}秒无新内容)")
                    raise TimeoutError(f"内容传输超时({content_timeout}秒)")

                # 显示思考进度
                if not content_started:
                    if elapsed > 180 and not thinking_warned:
                        print(f"\n💭 AI正在深度思考中，请耐心等待...")
                        thinking_warned = True

                    if current_time - last_thinking_update > thinking_interval:
                        thinking_dots = (thinking_dots + 1) % 4
                        dots = "." * thinking_dots + " " * (3 - thinking_dots)

                        if elapsed < 60:
                            print(f"\r📝 AI思考中{dots} ({elapsed}s)", end="", flush=True)
                        else:
                            minutes = elapsed // 60
                            seconds = elapsed % 60
                            print(f"\r📝 AI深度思考中{dots} ({minutes}m{seconds}s)", end="", flush=True)

                        last_thinking_update = current_time

                # 处理chunk数据
                if chunk.choices and len(chunk.choices) > 0:
                    choice = chunk.choices[0]

                    if hasattr(choice, 'delta') and choice.delta:
                        delta_content = getattr(choice.delta, 'content', None)
                        if delta_content:
                            if not content_started:
                                elapsed_final = int(current_time - response_start_time)
                                if elapsed_final < 60:
                                    print(f"\r✨ AI思考完成({elapsed_final}s)，正在接收响应...")
                                else:
                                    minutes = elapsed_final // 60
                                    seconds = elapsed_final % 60
                                    print(f"\r✨ AI思考完成({minutes}m{seconds}s)，正在接收响应...")
                                content_started = True

                            full_response += delta_content
                            last_content_time = current_time

                    if hasattr(choice, 'finish_reason') and choice.finish_reason:
                        if choice.finish_reason == 'stop':
                            break
                        elif choice.finish_reason == 'length':
                            print(f"\n⚠️ 响应因长度限制而截断")
                            break
                        elif choice.finish_reason == 'content_filter':
                            print(f"\n⚠️ 响应被内容过滤器拦截")
                            break
                        else:
                            print(f"\n⚠️ 响应异常结束: {choice.finish_reason}")
                            break

            # 响应完成处理
            if full_response:
                total_elapsed = int(time.time() - response_start_time)
                if total_elapsed < 60:
                    print(f"\r✅ 响应完成({total_elapsed}s)，内容长度: {len(full_response)} 字符")
                else:
                    minutes = total_elapsed // 60
                    seconds = total_elapsed % 60
                    print(f"\r✅ 响应完成({minutes}m{seconds}s)，内容长度: {len(full_response)} 字符")
                return full_response
            else:
                # 尝试非流式调用作为备用
                if chunk_count > 0:
                    print(f"\n🔄 尝试非流式调用作为备用方案...")
                    api_params_backup = api_params.copy()
                    api_params_backup["stream"] = False

                    backup_response = client.chat.completions.create(**api_params_backup)

                    if backup_response.choices and len(backup_response.choices) > 0:
                        backup_content = backup_response.choices[0].message.content
                        if backup_content:
                            print(f"✅ 非流式备用调用成功，内容长度: {len(backup_content)} 字符")
                            return backup_content

                return None

        except Exception as e:
            print(f"\n❌ API调用异常: {str(e)}")

            # 检查token超限错误
            error_str = str(e)
            if "maximum context length" in error_str and "tokens" in error_str:
                import re
                current_match = re.search(r'you requested about (\d+) tokens', error_str)
                max_match = re.search(r'maximum context length is (\d+) tokens', error_str)

                if current_match and max_match:
                    current_tokens = int(current_match.group(1))
                    max_tokens = int(max_match.group(1))
                    print(f"🎯 检测到token超限: 当前{current_tokens:,}, 最大{max_tokens:,}")
                    return f"TOKEN_LIMIT_EXCEEDED:{current_tokens}:{max_tokens}"

            raise e
    
    def update_config(self, step: str, params: Dict[str, Any]):
        """更新配置"""
        self.config["models"][step] = params
        self._save_config(self.config)
        print(f"✅ 已更新配置: {step}")
    
    def list_configs(self):
        """列出所有配置"""
        print("📋 当前模型配置:")
        for step, step_config in self.config["models"].items():
            model = step_config.get("model", "未设置")
            max_tokens = step_config.get("max_tokens", "未设置")
            reasoning = step_config.get("reasoning_effort", "N/A")
            print(f"  {step}: {model} (max_tokens: {max_tokens}, reasoning_effort: {reasoning})")

    def get_metrics(self) -> dict:
        """获取API调用指标"""
        return {
            'api_metrics': {
                'total_calls': self.metrics.total_calls,
                'successful_calls': self.metrics.successful_calls,
                'failed_calls': self.metrics.failed_calls,
                'empty_responses': self.metrics.empty_responses,
                'success_rate': self.metrics.success_rate,
                'failure_rate': self.metrics.failure_rate,
                'empty_response_rate': self.metrics.empty_response_rate,
                'average_response_time': self.metrics.average_response_time,
                'total_tokens_used': self.metrics.total_tokens_used,
                'error_counts': self.metrics.error_counts
            },
            'circuit_breaker': self.circuit_breaker.get_state(),
            'validation_stats': self.response_validator.get_validation_stats()
        }

    def print_metrics(self):
        """打印详细的性能指标"""
        metrics = self.get_metrics()
        api_metrics = metrics['api_metrics']

        print("\n" + "="*60)
        print("📊 AI API 性能指标报告")
        print("="*60)

        print(f"📞 总调用次数: {api_metrics['total_calls']}")
        print(f"✅ 成功调用: {api_metrics['successful_calls']}")
        print(f"❌ 失败调用: {api_metrics['failed_calls']}")
        print(f"🔍 空响应: {api_metrics['empty_responses']}")

        if api_metrics['total_calls'] > 0:
            print(f"📈 成功率: {api_metrics['success_rate']:.2%}")
            print(f"📉 失败率: {api_metrics['failure_rate']:.2%}")
            print(f"🕳️ 空响应率: {api_metrics['empty_response_rate']:.2%}")

        if api_metrics['successful_calls'] > 0:
            print(f"⏱️ 平均响应时间: {api_metrics['average_response_time']:.1f}秒")

        if api_metrics['total_tokens_used'] > 0:
            print(f"🎯 总Token使用: {api_metrics['total_tokens_used']:,}")

        # 错误统计
        if api_metrics['error_counts']:
            print(f"\n🚨 错误类型统计:")
            for error_type, count in sorted(api_metrics['error_counts'].items(),
                                          key=lambda x: x[1], reverse=True):
                print(f"  {error_type}: {count} 次")

        # 熔断器状态
        cb_state = metrics['circuit_breaker']
        print(f"\n🔴 熔断器状态: {cb_state['state']}")
        if cb_state['failure_count'] > 0:
            print(f"  失败次数: {cb_state['failure_count']}")

        # 验证统计
        validation_stats = metrics['validation_stats']
        if validation_stats:
            print(f"\n🔍 响应验证统计:")
            print(f"  总验证次数: {validation_stats.get('total_validations', 0)}")
            print(f"  验证成功率: {validation_stats.get('validation_success_rate', 0):.2%}")
            print(f"  平均置信度: {validation_stats.get('average_confidence', 0):.2f}")

            common_issues = validation_stats.get('common_issues', {})
            if common_issues:
                print(f"  常见问题:")
                for issue, count in list(common_issues.items())[:3]:
                    print(f"    {issue}: {count} 次")

        print("="*60)

    def call_api_optimized(self, step: str, messages: list, priority: int = 5,
                          expected_format: str = None, auto_optimize: bool = True) -> Optional[str]:
        """优化版API调用 - 集成Token管理和负载均衡"""

        # 获取模型参数
        params = self.get_model_params(step)
        model = params["model"]
        max_tokens_limit = params.get("max_tokens", 8000)

        # Token预检查和优化
        if auto_optimize:
            estimated_tokens = self.token_estimator.estimate_messages_tokens(messages)
            print(f"📊 预估Token使用: {estimated_tokens:,}")

            # 如果超过限制，自动优化
            if estimated_tokens > max_tokens_limit * 0.8:  # 80%阈值
                print(f"⚠️ Token使用接近限制，开始优化...")
                optimized_messages, final_tokens = self.token_estimator.optimize_messages(
                    messages, int(max_tokens_limit * 0.7)  # 保留30%缓冲
                )
                print(f"✅ Token优化完成: {estimated_tokens:,} → {final_tokens:,}")
                messages = optimized_messages

        # 选择最佳提供商
        available_providers = list(self.config["providers"].keys())
        selected_provider = self.load_balancer.select_provider(available_providers)

        if selected_provider:
            # 临时切换到选定的提供商
            original_provider = params.get("provider")
            params["provider"] = selected_provider
            print(f"🎯 选择提供商: {selected_provider}")

        # 创建请求元数据
        import uuid
        request_id = str(uuid.uuid4())[:8]
        metadata = RequestMetadata(
            request_id=request_id,
            step=step,
            priority=priority,
            estimated_tokens=self.token_estimator.estimate_messages_tokens(messages),
            max_tokens=max_tokens_limit
        )

        # 记录开始时间
        start_time = time.time()

        try:
            # 执行API调用
            result = self.call_api(step, messages, expected_format=expected_format)

            # 记录成功统计
            response_time = time.time() - start_time
            if selected_provider:
                self.load_balancer.update_provider_stats(
                    selected_provider, True, response_time
                )

            # 估算Token使用和成本
            if result:
                input_tokens = metadata.estimated_tokens
                output_tokens = self.token_estimator.estimate_tokens(result)
                token_usage = TokenUsage(
                    input_tokens=input_tokens,
                    output_tokens=output_tokens
                )
                token_usage.estimated_cost = self.token_estimator.calculate_cost(
                    token_usage, model
                )

                # 更新指标
                self.metrics.total_tokens_used += token_usage.total_tokens

                print(f"💰 Token使用: 输入{input_tokens:,} + 输出{output_tokens:,} = {token_usage.total_tokens:,}")
                print(f"💵 预估成本: ${token_usage.estimated_cost:.4f}")

            return result

        except Exception as e:
            # 记录失败统计
            response_time = time.time() - start_time
            error_type = self.error_classifier.classify_error(str(e))

            if selected_provider:
                self.load_balancer.update_provider_stats(
                    selected_provider, False, response_time, error_type.value
                )

            raise e

        finally:
            # 恢复原始提供商设置
            if selected_provider and original_provider:
                params["provider"] = original_provider

    def get_optimization_stats(self) -> dict:
        """获取优化统计信息"""
        return {
            'token_cache_size': len(self.token_estimator.cache),
            'queue_status': self.request_queue.get_queue_status(),
            'provider_stats': self.load_balancer.get_provider_stats(),
            'total_tokens_used': self.metrics.total_tokens_used
        }

    def print_optimization_stats(self):
        """打印优化统计信息"""
        stats = self.get_optimization_stats()

        print("\n" + "="*60)
        print("🚀 API 优化统计报告")
        print("="*60)

        print(f"🎯 Token缓存大小: {stats['token_cache_size']}")
        print(f"🔢 总Token使用: {stats['total_tokens_used']:,}")

        # 队列状态
        queue_status = stats['queue_status']
        print(f"\n📋 请求队列状态:")
        print(f"  排队请求: {queue_status['queued_requests']}")
        print(f"  活跃请求: {queue_status['active_requests']}")
        print(f"  已完成请求: {queue_status['completed_requests']}")
        print(f"  可处理新请求: {'是' if queue_status['can_process_new'] else '否'}")

        # 提供商统计
        provider_stats = stats['provider_stats']
        if provider_stats:
            print(f"\n🌐 提供商性能统计:")
            for provider, pstats in provider_stats.items():
                print(f"  {provider}:")
                print(f"    总请求: {pstats['total_requests']}")
                print(f"    成功率: {pstats['success_rate']:.2%}")
                print(f"    平均响应时间: {pstats['average_response_time']:.1f}s")
                print(f"    权重: {pstats['weight']:.2f}")

                if pstats['error_counts']:
                    print(f"    错误统计: {pstats['error_counts']}")

        print("="*60)

    def _setup_backup_endpoints(self):
        """设置备用端点"""
        # 从配置中添加备用端点
        for provider_name, provider_config in self.config["providers"].items():
            backup_endpoint = BackupEndpoint(
                name=f"backup_{provider_name}",
                base_url=provider_config["base_url"],
                api_key=provider_config["api_key"],
                priority=5,  # 默认优先级
                enabled=True
            )
            self.backup_manager.add_backup_endpoint(backup_endpoint)

        # 启动健康监控
        self.backup_manager.start_health_monitoring()

    def call_api_with_fallback(self, step: str, messages: list, max_retries: int = None,
                              expected_format: str = None, enable_fallback: bool = True) -> Optional[str]:
        """带故障恢复的API调用"""

        failed_providers = []
        fallback_activated = False

        # 首先尝试主要API调用
        try:
            result = self.call_api_optimized(step, messages, expected_format=expected_format)
            if result:
                return result
        except Exception as e:
            print(f"⚠️ 主API调用失败: {str(e)}")
            # 记录失败的提供商
            params = self.get_model_params(step)
            main_provider = params.get("provider", "gemini")
            failed_providers.append(main_provider)

        # 如果启用故障恢复，尝试备用端点
        if enable_fallback:
            backup_attempts = 0
            max_backup_attempts = 3

            while backup_attempts < max_backup_attempts:
                backup_endpoint = self.backup_manager.select_backup_endpoint(failed_providers)

                if not backup_endpoint:
                    print("❌ 没有可用的备用端点")
                    break

                backup_attempts += 1
                print(f"🔄 尝试备用端点: {backup_endpoint.name} (尝试 {backup_attempts}/{max_backup_attempts})")

                try:
                    # 临时切换到备用端点
                    original_config = self.config["providers"].copy()

                    # 创建临时提供商配置
                    temp_provider_name = f"temp_{backup_endpoint.name}"
                    self.config["providers"][temp_provider_name] = {
                        "api_key": backup_endpoint.api_key,
                        "base_url": backup_endpoint.base_url
                    }

                    # 更新步骤配置
                    params = self.get_model_params(step)
                    original_provider = params.get("provider")
                    params["provider"] = temp_provider_name

                    # 执行备用API调用
                    result = self.call_api(step, messages, max_retries=2, expected_format=expected_format)

                    if result:
                        print(f"✅ 备用端点调用成功: {backup_endpoint.name}")
                        self.backup_manager.record_endpoint_usage(backup_endpoint.name, True)
                        return result
                    else:
                        print(f"❌ 备用端点调用失败: {backup_endpoint.name}")
                        self.backup_manager.record_endpoint_usage(backup_endpoint.name, False)
                        failed_providers.append(backup_endpoint.name)

                except Exception as e:
                    print(f"❌ 备用端点异常: {backup_endpoint.name} - {str(e)}")
                    self.backup_manager.record_endpoint_usage(backup_endpoint.name, False)
                    failed_providers.append(backup_endpoint.name)

                finally:
                    # 恢复原始配置
                    self.config["providers"] = original_config
                    if original_provider:
                        params["provider"] = original_provider

            # 如果所有备用端点都失败，尝试降级策略
            if not fallback_activated:
                print("🔄 尝试降级策略...")
                error_type = "multiple_failures"
                failure_count = len(failed_providers)

                strategy_name = self.backup_manager.fallback_strategy.should_activate_fallback(
                    error_type, failure_count
                )

                if strategy_name:
                    context = {
                        'step': step,
                        'messages': messages,
                        'failed_providers': failed_providers,
                        'expected_format': expected_format
                    }

                    fallback_result = self.backup_manager.fallback_strategy.activate_fallback(
                        strategy_name, context
                    )

                    if fallback_result:
                        return fallback_result

        print(f"❌ 所有API调用尝试都失败了 (尝试了 {len(failed_providers)} 个端点)")
        return None

    def get_system_health(self) -> dict:
        """获取系统健康状态"""
        return {
            'api_metrics': self.get_metrics(),
            'optimization_stats': self.get_optimization_stats(),
            'backup_stats': self.backup_manager.get_backup_stats(),
            'circuit_breaker_state': self.circuit_breaker.get_state(),
            'timestamp': datetime.now().isoformat()
        }

    def print_system_health(self):
        """打印系统健康报告"""
        health = self.get_system_health()

        print("\n" + "="*80)
        print("🏥 AI API 系统健康报告")
        print("="*80)

        # API指标
        api_metrics = health['api_metrics']['api_metrics']
        print(f"📊 API调用统计:")
        print(f"  总调用: {api_metrics['total_calls']}")
        print(f"  成功率: {api_metrics['success_rate']:.2%}")
        print(f"  平均响应时间: {api_metrics['average_response_time']:.1f}s")

        # 熔断器状态
        cb_state = health['circuit_breaker_state']
        print(f"\n🔴 熔断器状态: {cb_state['state']}")

        # 备用系统状态
        backup_stats = health['backup_stats']
        print(f"\n🔗 备用系统:")
        print(f"  总端点: {backup_stats['total_endpoints']}")
        print(f"  可用端点: {backup_stats['enabled_endpoints']}")
        print(f"  健康端点: {backup_stats['healthy_endpoints']}")

        if backup_stats['active_fallbacks']:
            print(f"  激活的降级策略: {', '.join(backup_stats['active_fallbacks'])}")

        # Token使用统计
        opt_stats = health['optimization_stats']
        print(f"\n🎯 Token使用: {opt_stats['total_tokens_used']:,}")

        print("="*80)

    def call_api_enhanced(self, step: str, messages: list, priority: int = 5,
                         expected_format: str = None, enable_fallback: bool = True,
                         enable_profiling: bool = True) -> Optional[str]:
        """增强版API调用 - 集成所有优化功能"""

        operation_name = f"api_call_{step}"

        # 使用性能分析器
        if enable_profiling:
            with self.profiler.profile(operation_name, step=step, priority=priority) as metric:
                return self._execute_enhanced_api_call(
                    step, messages, priority, expected_format, enable_fallback, metric
                )
        else:
            return self._execute_enhanced_api_call(
                step, messages, priority, expected_format, enable_fallback
            )

    def _execute_enhanced_api_call(self, step: str, messages: list, priority: int,
                                  expected_format: str, enable_fallback: bool,
                                  metric=None) -> Optional[str]:
        """执行增强版API调用的内部方法"""

        self.logger.info('API_ENHANCED', f"开始增强版API调用",
                        context={
                            'step': step,
                            'priority': priority,
                            'expected_format': expected_format,
                            'enable_fallback': enable_fallback,
                            'message_count': len(messages)
                        })

        try:
            # 首先尝试优化版API调用
            result = self.call_api_optimized(step, messages, priority, expected_format)

            if result:
                self.logger.info('API_ENHANCED', "优化版API调用成功",
                               context={'response_length': len(result)})
                return result

            # 如果优化版失败且启用故障恢复，尝试故障恢复
            if enable_fallback:
                self.logger.warning('API_ENHANCED', "优化版API调用失败，尝试故障恢复")

                result = self.call_api_with_fallback(step, messages,
                                                   expected_format=expected_format,
                                                   enable_fallback=True)

                if result:
                    self.logger.info('API_ENHANCED', "故障恢复API调用成功",
                                   context={'response_length': len(result)})
                    return result

            # 所有方法都失败
            self.logger.error('API_ENHANCED', "所有API调用方法都失败")
            return None

        except Exception as e:
            self.logger.error('API_ENHANCED', "增强版API调用异常", exception=e,
                            context={'step': step, 'priority': priority})

            if metric:
                metric.metadata['error'] = str(e)

            raise e

    def get_diagnostic_report(self) -> str:
        """获取诊断报告"""
        return self.diagnostic_tool.generate_health_report()

    def enable_debug_mode(self):
        """启用调试模式"""
        self.debug_helper.enable_debug_mode()
        self.debug_helper.enable_call_tracing()
        self.logger.info('DEBUG', "调试模式已启用")

    def disable_debug_mode(self):
        """禁用调试模式"""
        self.debug_helper.disable_debug_mode()
        self.debug_helper.disable_call_tracing()
        self.logger.info('DEBUG', "调试模式已禁用")

    def export_logs(self, filename: str = None, format: str = 'json') -> str:
        """导出日志"""
        return self.logger.export_logs(filename, format)

    def get_comprehensive_stats(self) -> dict:
        """获取全面的系统统计信息"""
        return {
            'system_health': self.get_system_health(),
            'diagnostic_info': self.diagnostic_tool.diagnose_api_issues(),
            'performance_stats': self.profiler.get_performance_stats(),
            'log_summary': {
                'total_logs': len(self.logger.structured_logs),
                'error_count': len(self.logger.get_logs(level='ERROR')),
                'warning_count': len(self.logger.get_logs(level='WARNING'))
            }
        }

    def print_comprehensive_report(self):
        """打印全面的系统报告"""
        print("\n" + "="*100)
        print("🔍 AI API 系统全面报告")
        print("="*100)

        # 系统健康状态
        self.print_system_health()

        # 性能指标
        self.print_metrics()

        # 优化统计
        self.print_optimization_stats()

        # 诊断报告
        print("\n" + self.get_diagnostic_report())

        # 日志统计
        stats = self.get_comprehensive_stats()
        log_summary = stats['log_summary']

        print(f"\n📋 日志统计:")
        print(f"  总日志数: {log_summary['total_logs']}")
        print(f"  错误日志: {log_summary['error_count']}")
        print(f"  警告日志: {log_summary['warning_count']}")

        print("="*100)


# 全局配置实例
model_config = ModelConfig()


def get_model_config() -> ModelConfig:
    """获取全局模型配置实例"""
    return model_config 