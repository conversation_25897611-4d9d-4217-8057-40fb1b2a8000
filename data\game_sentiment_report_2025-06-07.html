<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏舆情可视化日报</title>
    <link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            /* 主色调 */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-accent: #EFF6FF;
            --accent-primary: #3B82F6;
            --accent-secondary: #10B981;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --border-light: #E5E7EB;
            --border-medium: #D1D5DB;
            
            /* 用户层级色彩 */
            --super-r: linear-gradient(135deg, #A855F7, #C084FC);
            --big-r: linear-gradient(135deg, #3B82F6, #60A5FA);
            --mid-r: linear-gradient(135deg, #10B981, #34D399);
            --small-r: linear-gradient(135deg, #F59E0B, #FBBF24);
            --zero-pay: linear-gradient(135deg, #8B5CF6, #A78BFA);
            
            /* 健康度色彩 */
            --health-excellent: #059669;
            --health-good: #0284C7;
            --health-warning: #EA580C;
            --health-critical: #DC2626;
            
            /* 风险等级色彩 */
            --risk-high: #DC2626;
            --risk-medium: #EA580C;
            --risk-low: #059669;
            
            /* 字体大小 */
            --font-title-main: 2.5rem;
            --font-title-sub: 2rem;
            --font-title-card: 1.5rem;
            --font-body: 1rem;
            --font-caption: 0.875rem;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', 'Microsoft YaHei', 'Segoe UI', system-ui, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        .dashboard-container {
            display: grid;
            grid-template-areas:
                "overview overview overview"
                "topics risks health"
                "compare wordcloud players";
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: auto 1fr 1fr;
            gap: 1.5rem;
            padding: 2rem;
            min-height: 100vh;
        }

        .card {
            background: var(--bg-secondary);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid var(--border-light);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            border-color: var(--border-medium);
        }

        .card h3 {
            color: var(--text-primary);
            font-size: var(--font-title-card);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .card h3 i {
            color: var(--accent-primary);
        }

        /* 主卡片：舆情总览 */
        .main-overview-card {
            grid-area: overview;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-accent) 100%);
        }

        .overview-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
        }

        .title-section h1 {
            font-size: var(--font-title-main);
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .title-section h1 i {
            color: var(--accent-primary);
        }

        .date-badge {
            background: var(--accent-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .health-score-large {
            display: flex;
            align-items: center;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            text-align: center;
            position: relative;
        }

        .score-value {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .score-label {
            font-size: var(--font-caption);
            margin-top: 0.25rem;
        }

        .global-metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .metric-item:hover {
            background: var(--bg-accent);
            transform: translateY(-1px);
        }

        .metric-item i {
            font-size: 1.5rem;
            color: var(--accent-primary);
            width: 40px;
            text-align: center;
        }

        .metric-content .value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-primary);
            display: block;
        }

        .metric-content .label {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .core-insight {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-primary);
        }

        .core-insight h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .core-insight p {
            color: var(--text-secondary);
            margin-bottom: 1rem;
            line-height: 1.7;
        }

        .insight-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .insight-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .insight-tag.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .insight-tag.warning {
            background: #FEF3C7;
            color: #92400E;
        }

        .insight-tag.positive {
            background: #DCFCE7;
            color: #166534;
        }

        /* 热点话题卡片 */
        .hot-topics-card {
            grid-area: topics;
        }

        .topics-ranking {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .topic-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--accent-secondary);
            transition: all 0.3s ease;
        }

        .topic-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .topic-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.75rem;
        }

        .topic-header .rank {
            background: var(--accent-secondary);
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-caption);
            font-weight: 600;
        }

        .topic-header .title {
            flex: 1;
            margin-left: 1rem;
            font-weight: 600;
        }

        .impact-badge {
            background: var(--bg-accent);
            color: var(--accent-primary);
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
        }

        .topic-details {
            display: flex;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        .negative-rate, .affected-levels {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .representative-quote {
            font-style: italic;
            color: var(--text-secondary);
            font-size: var(--font-caption);
            border-left: 2px solid var(--border-light);
            padding-left: 0.75rem;
        }

        /* 风险预警卡片 */
        .risk-radar-card {
            grid-area: risks;
        }

        .risk-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .risk-category {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            border-left: 4px solid var(--risk-medium);
        }

        .risk-category.severity-高 {
            border-left-color: var(--risk-high);
        }

        .risk-category.severity-中 {
            border-left-color: var(--risk-medium);
        }

        .risk-category.severity-低 {
            border-left-color: var(--risk-low);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .category-name {
            font-weight: 600;
            flex: 1;
        }

        .severity-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
            color: white;
            background: var(--risk-medium);
        }

        .severity-badge.severity-高 {
            background: var(--risk-high);
        }

        .severity-badge.severity-中 {
            background: var(--risk-medium);
        }

        .severity-badge.severity-低 {
            background: var(--risk-low);
        }

        .affected-groups {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .level-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: var(--font-caption);
            font-weight: 500;
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .risk-description {
            color: var(--text-secondary);
            font-size: var(--font-caption);
            line-height: 1.6;
        }

        /* 用户群体健康度卡片 */
        .user-health-card {
            grid-area: health;
        }

        .health-matrix {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .level-health-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .level-info {
            flex: 1;
        }

        .level-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .user-count {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .health-indicator {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            color: white;
            font-size: 1.2rem;
        }

        .level-status {
            text-align: center;
        }

        .status-text {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            display: block;
        }

        .trend-arrow {
            font-size: 1.5rem;
            margin-top: 0.25rem;
        }

        /* 版本对比卡片 */
        .version-compare-card {
            grid-area: compare;
        }

        .compare-container {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .version-column {
            flex: 1;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
        }

        .version-column h4 {
            margin-bottom: 1rem;
            color: var(--accent-primary);
            font-size: 1.2rem;
        }

        .version-metrics {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .version-metrics .health-score {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .version-metrics .risk-count,
        .version-metrics .hot-issue {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .vs-divider {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--accent-primary);
        }

        .key-differences {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
        }

        .key-differences h5 {
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .key-differences ul {
            list-style: none;
        }

        .key-differences li {
            padding: 0.5rem 0;
            border-bottom: 1px solid var(--border-light);
            color: var(--text-secondary);
            font-size: var(--font-caption);
        }

        /* 词云卡片 */
        .wordcloud-card {
            grid-area: wordcloud;
        }

        .word-cloud-container {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            min-height: 200px;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .word-bubble {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .word-bubble[data-sentiment="negative"] {
            background: #FEE2E2;
            color: #991B1B;
        }

        .word-bubble[data-sentiment="neutral"] {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .word-bubble[data-sentiment="positive"] {
            background: #DCFCE7;
            color: #166534;
        }

        .word-bubble:hover {
            transform: scale(1.1);
        }

        .sentiment-legend {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: var(--font-caption);
        }

        .legend-item:before {
            content: '';
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .legend-item.positive:before {
            background: #166534;
        }

        .legend-item.neutral:before {
            background: var(--accent-primary);
        }

        .legend-item.negative:before {
            background: #991B1B;
        }

        /* 重点关注玩家卡片 */
        .key-players-card {
            grid-area: players;
        }

        .players-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .player-item {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .player-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--accent-primary);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .player-info {
            flex: 1;
        }

        .player-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .player-level {
            font-size: var(--font-caption);
            color: var(--text-secondary);
        }

        .player-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
        }

        .message-count {
            font-weight: 600;
            color: var(--text-primary);
        }

        .sentiment-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: var(--font-caption);
            font-weight: 500;
        }

        .sentiment-badge.negative {
            background: #FEE2E2;
            color: #991B1B;
        }

        .sentiment-badge.neutral {
            background: var(--bg-accent);
            color: var(--accent-primary);
        }

        .sentiment-badge.positive {
            background: #DCFCE7;
            color: #166534;
        }

        .attention-reason {
            font-size: var(--font-caption);
            color: var(--text-secondary);
            line-height: 1.5;
            max-width: 200px;
        }

        /* 健康度分数样式 */
        .health-indicator[data-score^="8"],
        .health-indicator[data-score^="9"],
        .score-circle[data-score^="8"],
        .score-circle[data-score^="9"] {
            background: var(--health-excellent);
        }

        .health-indicator[data-score^="6"],
        .health-indicator[data-score^="7"],
        .score-circle[data-score^="6"],
        .score-circle[data-score^="7"] {
            background: var(--health-good);
        }

        .health-indicator[data-score^="4"],
        .health-indicator[data-score^="5"],
        .score-circle[data-score^="4"],
        .score-circle[data-score^="5"] {
            background: var(--health-warning);
        }

        .health-indicator[data-score^="0"],
        .health-indicator[data-score^="1"],
        .health-indicator[data-score^="2"],
        .health-indicator[data-score^="3"],
        .score-circle[data-score^="0"],
        .score-circle[data-score^="1"],
        .score-circle[data-score^="2"],
        .score-circle[data-score^="3"] {
            background: var(--health-critical);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .dashboard-container {
                grid-template-areas:
                    "overview overview"
                    "topics risks"
                    "health compare"
                    "wordcloud players";
                grid-template-columns: 1fr 1fr;
            }

            .global-metrics-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                grid-template-areas:
                    "overview"
                    "topics"
                    "risks"
                    "health"
                    "compare"
                    "wordcloud"
                    "players";
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .global-metrics-grid {
                grid-template-columns: 1fr;
            }

            .overview-header {
                flex-direction: column;
                gap: 1rem;
            }

            .compare-container {
                flex-direction: column;
            }

            .vs-divider {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 主卡片：舆情总览 -->
        <div class="main-overview-card card">
            <div class="overview-header">
                <div class="title-section">
                    <h1><i class="fas fa-gamepad"></i> 游戏舆情日报</h1>
                    <div class="date-badge">2025年06月07日</div>
                </div>
                <div class="health-score-large">
                    <div class="score-circle" data-score="55">
                        <span class="score-value">55</span>
                        <span class="score-label">综合健康度</span>
                    </div>
                </div>
            </div>
            
            <div class="global-metrics-grid">
                <div class="metric-item">
                    <i class="fas fa-comments"></i>
                    <div class="metric-content">
                        <span class="value">294,239</span>
                        <span class="label">总消息数</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-users"></i>
                    <div class="metric-content">
                        <span class="value">33,645</span>
                        <span class="label">活跃用户</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="metric-content">
                        <span class="value">10</span>
                        <span class="label">高风险警报</span>
                    </div>
                </div>
                <div class="metric-item">
                    <i class="fas fa-chart-line"></i>
                    <div class="metric-content">
                        <span class="value">40.7%</span>
                        <span class="label">负面情绪占比</span>
                    </div>
                </div>
            </div>
            
            <div class="core-insight">
                <h3><i class="fas fa-lightbulb"></i> 今日核心洞察</h3>
                <p>今日舆情显示，游戏健康度面临严峻挑战，核心痛点集中爆发。**装备洗练**系统随机性过强、回报率低，已成为各付费层级，尤其是大R和超R用户的首要不满来源，直接引发付费意愿下降和退游风险。同时，E版用户普遍反映**战损和治疗成本**过高，‘死不起兵’严重打击了PVP参与热情。跨服匹配不公和网络卡顿问题进一步恶化了核心玩法体验。需紧急关注核心付费用户的流失预警，优化养成系统和战斗经济模型。</p>
                <div class="insight-tags">
                    <span class="insight-tag negative">核心付费体验恶化</span>
<span class="insight-tag warning">高额战损抑制PVP</span>
<span class="insight-tag negative">核心用户流失风险</span>
                </div>
            </div>
        </div>

        <!-- 热点话题卡片 -->
        <div class="hot-topics-card card">
            <h3><i class="fas fa-fire"></i> 全服热点话题</h3>
            <div class="topics-ranking">
                <div class="topic-item">
    <div class="topic-header">
        <span class="rank">#1</span>
        <span class="title">战损过高，医院爆满</span>
        <span class="impact-badge">讨论量: 极高</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 80%+</div>
        <div class="affected-levels">影响用户层级: 小R, 中R, 大R</div>
    </div>
    <div class="representative-quote">"我医院爆了，治疗死我了，死10万T11啦，伤不起"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#2</span>
        <span class="title">角色属性与战力提升</span>
        <span class="impact-badge">讨论量: 高</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 45%</div>
        <div class="affected-levels">影响用户层级: 大R</div>
    </div>
    <div class="representative-quote">"我劝你别急着拉战力，留着点资源搞加成，战力高没啥用的都是虚战"</div>
</div>
<div class="topic-item">
    <div class="topic-header">
        <span class="rank">#3</span>
        <span class="title">装备洗练随机性过强</span>
        <span class="impact-badge">讨论量: 高</span>
    </div>
    <div class="topic-details">
        <div class="negative-rate">负面率 75%+</div>
        <div class="affected-levels">影响用户层级: 中R, 大R, 超R</div>
    </div>
    <div class="representative-quote">"30天后，洗不出来就退游了"</div>
</div>
            </div>
        </div>

        <!-- 风险预警雷达 -->
        <div class="risk-radar-card card">
            <h3><i class="fas fa-exclamation-triangle"></i> 风险预警雷达</h3>
            <div class="risk-matrix">
                <div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">核心用户流失风险</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
    </div>
    <div class="risk-description">核心付费用户因“洗练”系统回报率低、新内容推出过快导致氪金疲劳，以及高昂的战损成本，已产生强烈退游意向，对游戏收入和生态构成严重威胁。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">付费/核心玩法体验差</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">超R</span>
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
        <span class="level-tag">小R</span>
    </div>
    <div class="risk-description">各层级用户普遍反映PVP体验差。高付费用户因“克制”机制和养成随机性感觉投入未获回报；中低付费用户则因战力差距过大、战损成本过高而选择避战，严重打击参战积极性。</div>
</div>
<div class="risk-category severity-高">
    <div class="category-header">
        <span class="category-name">社区/技术问题</span>
        <span class="severity-badge severity-高">高</span>
    </div>
    <div class="affected-groups">
        <span class="level-tag">大R</span>
        <span class="level-tag">中R</span>
    </div>
    <div class="risk-description">游戏在关键时段的网络卡顿、掉线问题频发，严重影响核心玩法。同时，部分服务器社区环境恶化，谩骂冲突频发，影响游戏氛围。</div>
</div>
            </div>
        </div>

        <!-- 用户群体健康度 -->
        <div class="user-health-card card">
            <h3><i class="fas fa-users"></i> 用户群体健康度</h3>
            <div class="health-matrix">
                <div class="level-health-item">
    <div class="level-info">
        <div class="level-name">超R用户</div>
        <div class="user-count">713人</div>
    </div>
    <div class="health-indicator" data-score="55">
        <span class="score">55</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">大R用户</div>
        <div class="user-count">2,410人</div>
    </div>
    <div class="health-indicator" data-score="62">
        <span class="score">62</span>
    </div>
    <div class="level-status">
        <span class="status-text">良好</span>
        <span class="trend-arrow">➡️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">中R用户</div>
        <div class="user-count">2,293人</div>
    </div>
    <div class="health-indicator" data-score="55">
        <span class="score">55</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">小R用户</div>
        <div class="user-count">3,418人</div>
    </div>
    <div class="health-indicator" data-score="56">
        <span class="score">56</span>
    </div>
    <div class="level-status">
        <span class="status-text">一般</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
<div class="level-health-item">
    <div class="level-info">
        <div class="level-name">零氪用户</div>
        <div class="user-count">1,756人</div>
    </div>
    <div class="health-indicator" data-score="38">
        <span class="score">38</span>
    </div>
    <div class="level-status">
        <span class="status-text">较差</span>
        <span class="trend-arrow">⬇️</span>
    </div>
</div>
            </div>
        </div>

        <!-- 版本对比分析 -->
        <div class="version-compare-card card">
            <h3><i class="fas fa-balance-scale"></i> 版本对比分析</h3>
            <div class="compare-container">
                <div class="version-column">
                    <h4>E版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">53</div>
                        <div class="risk-count">8个风险点</div>
                        <div class="hot-issue">战损过高，医院爆满</div>
                    </div>
                </div>
                <div class="vs-divider">VS</div>
                <div class="version-column">
                    <h4>D版本</h4>
                    <div class="version-metrics">
                        <div class="health-score">62</div>
                        <div class="risk-count">2个风险点</div>
                        <div class="hot-issue">角色属性与战力提升</div>
                    </div>
                </div>
            </div>
            <div class="key-differences">
                <h5>关键差异</h5>
                <ul>
                    <li>E版用户普遍聚焦于高昂战损和治疗成本，而D版大R用户更关注角色属性和战力提升的细节。</li>
<li>E版各层级均有大量高风险预警（8个），问题普遍化；D版风险集中于大R用户（2个），问题更具针对性。</li>
<li>E版的核心痛点是“洗练”和“死兵”，直接影响PVP参与度；D版痛点在于“克制”机制和养成随机性，影响付费体验。</li>
<li>E版用户对网络卡顿、掉线等技术问题抱怨更多，尤其是在核心PVP时段。</li>
                </ul>
            </div>
        </div>

        <!-- 舆情词云分析 -->
        <div class="wordcloud-card card">
            <h3><i class="fas fa-tags"></i> 舆情词云分析</h3>
            <div class="word-cloud-container">
                <span class="word-bubble" data-sentiment="negative" data-frequency="1150" style="font-size: 2rem;">医院</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="845" style="font-size: 2rem;">兽王</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="810" style="font-size: 2rem;">打不过</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="790" style="font-size: 2rem;">集结</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="776" style="font-size: 2rem;">资源</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="763" style="font-size: 2rem;">首都</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="748" style="font-size: 2rem;">属性</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="664" style="font-size: 2rem;">跨服</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="561" style="font-size: 2rem;">治疗</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="550" style="font-size: 2rem;">死兵</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="490" style="font-size: 1.8rem;">大佬</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="450" style="font-size: 1.8rem;">开罩</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="304" style="font-size: 1.8rem;">洗练</span>
<span class="word-bubble" data-sentiment="neutral" data-frequency="250" style="font-size: 1.6rem;">加成</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="138" style="font-size: 1.4rem;">卡</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="115" style="font-size: 1.4rem;">退游</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="100" style="font-size: 1.4rem;">掉线</span>
<span class="word-bubble" data-sentiment="negative" data-frequency="80" style="font-size: 1.2rem;">克制</span>
            </div>
            <div class="sentiment-legend">
                <span class="legend-item positive">正面词汇</span>
                <span class="legend-item neutral">中性词汇</span>
                <span class="legend-item negative">负面词汇</span>
            </div>
        </div>

        <!-- 重点关注玩家 -->
        <div class="key-players-card card">
            <h3><i class="fas fa-crown"></i> 重点关注玩家</h3>
            <div class="players-list">
                <div class="player-item">
    <div class="player-avatar">小</div>
    <div class="player-info">
        <div class="player-name">小桃气</div>
        <div class="player-level">超R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">105条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">核心付费用户，对高战损、氪金回报低等问题有强烈不满，是潜在的流失高危人群和意见领袖。</div>
</div>
<div class="player-item">
    <div class="player-avatar">天</div>
    <div class="player-info">
        <div class="player-name">天策上将</div>
        <div class="player-level">大R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">42条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">高频发言的KOL，观点犀利，对游戏内数值和付费点极度敏感，其负面言论有较强传播力。</div>
</div>
<div class="player-item">
    <div class="player-avatar">离</div>
    <div class="player-info">
        <div class="player-name">离垢</div>
        <div class="player-level">中R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">48条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">高频发言，言辞犀利，对游戏机制和运营有深度不满，是典型的负面意见领袖。</div>
</div>
<div class="player-item">
    <div class="player-avatar">世</div>
    <div class="player-info">
        <div class="player-name">世界第一帅</div>
        <div class="player-level">小R用户</div>
    </div>
    <div class="player-stats">
        <span class="message-count">58条</span>
        <span class="sentiment-badge negative">负面</span>
    </div>
    <div class="attention-reason">发言频繁且情绪化，抱怨游戏机制和玩家关系，有流失和带节奏风险。</div>
</div>
            </div>
        </div>
    </div>

    <script>
        // 简单的交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为词云添加点击效果
            const wordBubbles = document.querySelectorAll('.word-bubble');
            wordBubbles.forEach(bubble => {
                bubble.addEventListener('click', function() {
                    const sentiment = this.getAttribute('data-sentiment');
                    const frequency = this.getAttribute('data-frequency') || Math.floor(Math.random() * 100) + 1;
                    alert(`词汇: ${this.textContent}\n情感倾向: ${sentiment}\n出现频次: ${frequency}`);
                });
            });

            // 为健康度指示器添加悬停效果
            const healthIndicators = document.querySelectorAll('.health-indicator, .score-circle');
            healthIndicators.forEach(indicator => {
                indicator.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1)';
                });
                
                indicator.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // 为卡片添加轻微的3D效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = this.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;
                    
                    const rotateX = (y - centerY) / 20;
                    const rotateY = (centerX - x) / 20;
                    
                    this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-2px)`;
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0)';
                });
            });
        });
    </script>
</body>
</html>