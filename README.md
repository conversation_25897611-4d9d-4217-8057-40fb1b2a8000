# 每日言论 - 游戏舆情AI分析系统

## 📋 项目简介

**每日言论**是一个基于AI的游戏舆情自动化分析系统，专为游戏运营团队设计。系统能够自动从数据库获取用户言论数据，使用Google Gemini 2.5 Pro进行智能分析，生成可视化HTML报告，并通过企业微信机器人自动推送分析结果。

### ✨ 核心价值

- 🤖 **AI智能分析** - 使用最新的Gemini 2.5 Pro模型进行深度情感分析
- 🎯 **精准分群** - 按付费层级（零氪、小R、中R、大R、超R）分类分析
- 📊 **可视化报告** - 生成专业的HTML数据报告，支持移动端查看
- 🚀 **自动化流程** - 从数据获取到报告推送的全流程自动化
- 🔄 **多版本支持** - 同时分析D版和E版游戏数据

## 🛠️ 核心功能

### 1. 数据收集与AI分析 (`ai_analysis.py`)
- **多版本并发**: 同时分析D版和E版游戏数据
- **智能分群**: 按付费层级自动分类用户言论
- **深度分析**: 基于用户ID的个人行为模式分析
- **并发处理**: 10个线程同时分析，提升效率
- **智能采样**: 每个层级最多分析1000条代表性言论
- **🔄 重试机制**: SQL查询失败时自动重试3次，提升数据获取成功率

### 2. 可视化报告生成 (`html_generator.py`)
- **响应式设计**: 适配移动端和桌面端查看
- **数据可视化**: 丰富的图表和统计展示
- **交互体验**: 支持数据交互展示
- **主题美化**: 现代化UI设计

### 3. 图片转换 (`html_to_image.py`)
- **高质量截图**: 基于Playwright的高清图片生成
- **智能压缩**: 自动优化文件大小至2MB以内
- **移动适配**: 适合手机端查看和分享

### 4. 企业微信推送 (`wecom_bot.py`)
- **多消息类型**: 支持文本、Markdown、图片推送
- **@用户功能**: 支持特定用户提醒
- **错误重试**: 完善的重试机制确保推送成功

### 5. 统一流程控制 (`main.py`)
- **一键运行**: 集成所有功能的统一入口
- **演示模式**: 支持使用模拟数据进行功能演示
- **进度显示**: 详细的执行进度和状态反馈
- **自动清理**: 智能管理历史文件

## 📊 分析维度

### 基础统计指标
- 消息总数和有效消息数
- 独立用户数量统计
- 负面情绪比例分析

### 热点话题识别
- **TOP5热门话题** - 基于词频和讨论热度
- **话题分类** - 技术问题/体验问题/收入相关/社交互动/其他
- **关键词分析** - 高频词汇统计和趋势
- **代表性言论** - 提取典型用户发言

### 情绪分析
- **多维度情绪** - 正面/负面/中性情绪分布
- **话题情绪** - 各话题对应的情绪趋势
- **用户情绪** - 个人用户的情绪倾向分析

### 风险预警系统
- **风险类型识别** - 自动识别潜在风险点
- **严重程度评估** - 量化风险等级
- **具体风险描述** - 详细的风险说明和建议

### 活跃玩家分析
- **高频用户识别** - 发言3条以上的活跃用户
- **个人行为分析** - 用户发言模式和偏好
- **重点关注标记** - 需要特别关注的用户
- **话题偏好分析** - 用户兴趣话题统计

### 群体洞察分析
- **共同痛点识别** - 不同付费层级的共性问题
- **群体特征分析** - 各层级用户的独特特点
- **留存风险预警** - 用户流失风险评估
- **参与模式分析** - 用户活跃度和参与度

## 🚀 快速开始

### 环境要求
- Python 3.8+
- 网络连接（用于API调用）
- 数据库访问权限

### 1. 一键安装
```bash
# 克隆项目
git clone <repository-url>
cd 每日言论

# 运行安装脚本（推荐）
chmod +x setup.sh
./setup.sh
```

### 2. 手动安装
```bash
# 安装Python依赖
pip3 install -r requirements.txt

# 安装Playwright浏览器
playwright install chromium
```

### 3. 配置（可选）
```bash
# 配置企业微信机器人（可选）
export WECOM_BOT_WEBHOOK='你的企业微信机器人webhook地址'
```

### 4. 运行系统
```bash
# 完整分析流程（推荐）
python3 main.py

# 演示模式（使用模拟数据）
python3 main.py --demo

# 查看帮助信息
python3 main.py --help

# 测试图片生成和推送功能
python3 test_image_and_push.py

# 测试SQL重试机制
python3 test_retry.py
```

## 📁 项目结构

```
每日言论/
├── main.py                    # 🎯 主程序入口
├── ai_analysis.py             # 🤖 AI分析核心模块
├── html_generator.py          # 📊 HTML报告生成器
├── html_to_image.py           # 🖼️ HTML转图片模块
├── wecom_bot.py              # 💬 企业微信推送模块
├── TA.py                     # 🗄️ 数据库查询接口
├── SET.py                    # ⚙️ 配置管理
├── test_image_and_push.py    # 🧪 功能测试脚本
├── setup.sh                  # 🛠️ 快速安装脚本
├── requirements.txt          # 📦 依赖包列表
├── prompt1.md               # 📝 AI分析提示词模板
├── prompt2.md               # 📝 HTML生成提示词模板
├── data/                    # 📂 数据文件夹
│   ├── ai_analysis_*.json   # AI分析结果
│   ├── raw_data_*.json      # 原始查询数据
│   └── ...
└── README.md               # 📖 项目文档
```

## 🔧 配置说明

### API配置
```python
# Gemini API (已配置)
GEMINI_MODEL = "gemini-2.5-pro-preview-06-05"
API_KEY = "已配置"
```

### 数据库配置
- 支持的游戏版本：D版、E版
- 数据源：数数平台
- 付费层级：零氪、小R、中R、大R、超R

### 性能参数
- 并发线程数：10个（2版本×5付费层级）
- 单次分析耗时：约40秒
- 数据采样：每组最多1000条言论

## 📈 输出文件

运行系统后会在 `data/` 目录生成以下文件：

```
data/
├── ai_analysis_2024-01-20.json           # 🤖 AI分析结果（JSON格式）
├── game_sentiment_report_2024-01-20.html # 📊 可视化HTML报告
├── game_sentiment_report_2024-01-20_screenshot.jpg # 🖼️ 报告截图（≤2MB）
└── raw_data_版本_层级_2024-01-20.json    # 📂 原始查询数据
```

### 分析结果结构
```json
{
  "版本-付费层级": {
    "ai_response": {
      "basic_info": {
        "pay_level": "用户付费层级",
        "total_messages": "消息总数",
        "valid_messages": "有效消息数",
        "negative_sentiment_ratio": "负面情绪比例"
      },
      "top_topics": ["热门话题列表"],
      "risk_alerts": ["风险预警信息"],
      "active_players": [
        {
          "player_id": "用户ID",
          "role_name": "角色名",
          "message_count": "发言条数",
          "sentiment_tendency": "情绪倾向",
          "attention_level": "关注级别"
        }
      ],
      "group_insights": {
        "common_pain_points": ["群体痛点"],
        "unique_characteristics": "群体特征",
        "retention_risks": ["流失风险"]
      }
    },
    "metadata": {
      "total_comments": "查询到的总条数",
      "analyzed_comments": "实际分析条数",
      "unique_users": "独立用户数"
    }
  }
}
```

## 🎯 使用场景

### 日常运营
- **每日舆情监控** - 自动化的日报生成
- **版本对比分析** - D版与E版用户反馈对比
- **付费用户关怀** - 不同付费层级的针对性分析

### 决策支持
- **风险预警** - 及时发现潜在问题
- **热点话题追踪** - 了解用户关注焦点
- **用户流失预防** - 识别高风险用户

### 团队协作
- **企业微信推送** - 及时的团队信息同步
- **移动端查看** - 随时随地查看分析报告
- **数据归档** - 历史数据的完整保存

## 🚨 常见问题

### Q: 如何处理API调用失败？
A: 系统内置了完善的重试机制，最多重试6次。如果持续失败，请检查网络连接和API密钥。

### Q: 如何自定义分析维度？
A: 修改 `prompt1.md` 文件中的提示词模板，调整AI分析的焦点和输出格式。

### Q: 如何配置企业微信推送？
A: 设置环境变量 `WECOM_BOT_WEBHOOK`，或在代码中直接配置机器人地址。

### Q: 系统支持哪些数据源？
A: 目前支持数数平台的D版和E版游戏数据，可以通过修改 `TA.py` 适配其他数据源。

## 📊 性能指标

- **分析速度**: 约40秒完成10个用户群体分析
- **数据覆盖**: 支持20万+条言论的智能采样分析
- **准确性**: 基于Gemini 2.5 Pro的高准确度AI分析
- **可靠性**: 多层重试机制，确保系统稳定运行

## 🔮 技术特点

1. **并发处理** - 多线程并发分析，大幅提升效率
2. **智能采样** - 避免API限制，确保分析质量
3. **用户维度** - 按用户ID组织数据，支持个人行为分析
4. **错误处理** - 完善的异常处理和重试机制
5. **格式清理** - 自动清理AI响应中的格式标记

## 📞 技术支持

如遇到问题或需要功能定制，请：
1. 查看系统日志输出
2. 检查配置文件设置  
3. 验证网络连接状态
4. 联系技术团队获取支持

---

**每日言论** - 让游戏舆情分析更智能、更高效 🚀 