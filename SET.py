# -*- coding: utf-8 -*-
"""精简配置类 - 只保留SQL查询核心功能"""

class Setting:
    def __init__(self, game):
        self.game = game
        
        if game == 'D':
            self.token = 'AlzoC61vubwFW7y2OinO26lxL44OLvQ11TjDvh1tVDKuedCQn5UPlx1dFoVRrbG0'
            self.sql_url = 'http://ss-cn-search.kkk5.com:8992/querySql'
            self.start_date = '2021-11-17'
        elif game == 'E':
            self.token = 'SwTk02mFqlauUanypnm4MHe93BAK1WmrFWPyc5lp0WDvKEaa0GMO3xG1QN78Kh3a'
            self.sql_url = 'http://ss-cn-search.kkk5.com:8992/querySql'
            self.start_date = '2023-04-06' 